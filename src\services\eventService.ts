import { ApiService } from "./apiService";
import { API_ENDPOINTS } from "../config/api";
import type { Event, ApiResponse, EventListItem } from "../types";

export class EventService {
  /**
   * Get all events for the current user
   */
  static async getEvents(): Promise<EventListItem[]> {
    try {
      const response = await ApiService.get<ApiResponse<EventListItem[]>>(API_ENDPOINTS.EVENTS.LIST);
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to fetch events");
    } catch (error) {
      console.error("Get events error:", error);
      throw error;
    }
  }

  /**
   * Get a specific event by ID
   */
  static async getEvent(eventId: string): Promise<Event> {
    try {
      const response = await ApiService.get<ApiResponse<Event>>(API_ENDPOINTS.EVENTS.GET(eventId));
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to fetch event");
    } catch (error) {
      console.error("Get event error:", error);
      throw error;
    }
  }





  /**
   * Delete an event
   */
  static async deleteEvent(eventId: string): Promise<void> {
    try {
      const response = await ApiService.delete<ApiResponse<void>>(API_ENDPOINTS.EVENTS.DELETE(eventId));
      
      if (response.status !== 200) {
        throw new Error(response.message || "Failed to delete event");
      }
    } catch (error) {
      console.error("Delete event error:", error);
      throw error;
    }
  }

  /**
   * Publish an event (change status from draft to published)
   */
  static async publishEvent(eventId: string): Promise<Event> {
    try {
      const response = await ApiService.patch<ApiResponse<Event>>(
        API_ENDPOINTS.EVENTS.PUBLISH(eventId),
        {}
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to publish event");
    } catch (error) {
      console.error("Publish event error:", error);
      throw error;
    }
  }

  /**
   * Cancel an event
   */
  static async cancelEvent(eventId: string): Promise<Event> {
    try {
      const response = await ApiService.patch<ApiResponse<Event>>(
        API_ENDPOINTS.EVENTS.CANCEL(eventId),
        {}
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to cancel event");
    } catch (error) {
      console.error("Cancel event error:", error);
      throw error;
    }
  }

  /**
   * Complete an event
   */
  static async completeEvent(eventId: string): Promise<Event> {
    try {
      const response = await ApiService.patch<ApiResponse<Event>>(
        API_ENDPOINTS.EVENTS.COMPLETE(eventId),
        {}
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to complete event");
    } catch (error) {
      console.error("Complete event error:", error);
      throw error;
    }
  }

  /**
   * Duplicate an event
   */
  static async duplicateEvent(eventId: string, newName?: string): Promise<Event> {
    try {
      const response = await ApiService.post<ApiResponse<Event>>(
        API_ENDPOINTS.EVENTS.DUPLICATE(eventId),
        { name: newName }
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to duplicate event");
    } catch (error) {
      console.error("Duplicate event error:", error);
      throw error;
    }
  }
}