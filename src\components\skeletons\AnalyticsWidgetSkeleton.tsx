import React from 'react';

interface AnalyticsWidgetSkeletonProps {
  className?: string;
  showCharts?: boolean;
}

const AnalyticsWidgetSkeleton: React.FC<AnalyticsWidgetSkeletonProps> = ({
  className = "",
  showCharts = true,
}) => {
  return (
    <div className={`space-y-6 animate-pulse transition-opacity duration-300 ease-in-out ${className}`}>
      {/* Quick Stats Skeleton */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                {/* Icon skeleton */}
                <div className="h-5 w-5 bg-gray-200 rounded" />
              </div>
              <div className="ml-3 flex-1">
                {/* Label skeleton */}
                <div className="h-3 bg-gray-200 rounded w-16 mb-2" />
                {/* Value skeleton */}
                <div className="h-5 bg-gray-200 rounded w-12" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Skeleton */}
      {showCharts && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Chart 1 Skeleton */}
          <div className="bg-white rounded-lg shadow p-6">
            {/* Chart header skeleton */}
            <div className="flex items-center mb-4">
              <div className="h-5 w-5 bg-gray-200 rounded mr-2" />
              <div className="h-5 bg-gray-200 rounded w-32" />
            </div>
            {/* Chart area skeleton */}
            <div className="h-48 bg-gray-100 rounded flex items-end justify-center space-x-2 p-4">
              {[...Array(5)].map((_, index) => (
                <div
                  key={index}
                  className="bg-gray-200 rounded-t"
                  style={{
                    height: `${Math.random() * 80 + 20}%`,
                    width: '20px',
                  }}
                />
              ))}
            </div>
          </div>

          {/* Chart 2 Skeleton */}
          <div className="bg-white rounded-lg shadow p-6">
            {/* Chart header skeleton */}
            <div className="h-5 bg-gray-200 rounded w-28 mb-4" />
            {/* Pie chart skeleton */}
            <div className="h-48 bg-gray-100 rounded flex items-center justify-center">
              <div className="w-32 h-32 bg-gray-200 rounded-full" />
            </div>
          </div>
        </div>
      )}

      {/* Top Events Summary Skeleton */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          {/* Header skeleton */}
          <div className="h-5 bg-gray-200 rounded w-24" />
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div
                key={index}
                className="flex items-center justify-between"
              >
                <div className="flex-1 min-w-0">
                  {/* Event name skeleton */}
                  <div className="h-4 bg-gray-200 rounded w-48 mb-1" />
                  {/* Event location skeleton */}
                  <div className="h-3 bg-gray-200 rounded w-32" />
                </div>
                <div className="flex items-center space-x-4">
                  {/* Stats skeletons */}
                  {[...Array(3)].map((_, statIndex) => (
                    <div key={statIndex} className="text-center">
                      <div className="h-4 bg-gray-200 rounded w-8 mb-1" />
                      <div className="h-3 bg-gray-200 rounded w-12" />
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsWidgetSkeleton;