import { AlertTriangle, RefreshCw } from "lucide-react";
import type { DashboardError } from "../types";
import ErrorDisplay from "./ErrorDisplay";

interface DashboardErrorDisplayProps {
  error: DashboardError;
  onRetry?: () => void;
  isRetrying?: boolean;
  section?: 'stats' | 'events' | 'analytics' | 'full';
}

const DashboardErrorDisplay = ({ 
  error, 
  onRetry, 
  isRetrying = false, 
  section = 'full' 
}: DashboardErrorDisplayProps) => {
  // For section-specific errors, show a more compact error display
  if (section !== 'full') {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5 text-red-400" />
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-red-800">
              Failed to load {section}
            </h3>
            <p className="mt-1 text-sm text-red-700">
              {error.message}
            </p>
            {error.retryable && onRetry && (
              <div className="mt-3">
                <button
                  onClick={onRetry}
                  disabled={isRetrying}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                >
                  <RefreshCw className={`h-3 w-3 mr-1 ${isRetrying ? 'animate-spin' : ''}`} />
                  {isRetrying ? 'Retrying...' : 'Retry'}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // For full dashboard errors, use the main error display
  return (
    <div className="min-h-[400px] flex items-center justify-center">
      <ErrorDisplay 
        error={error} 
        onRetry={onRetry} 
        isRetrying={isRetrying}
        className="max-w-md"
      />
    </div>
  );
};

export default DashboardErrorDisplay;