import axios from "axios";
import type { AxiosRequestConfig, AxiosError } from "axios";
import { useAuthStore } from "../store/authStore";
import { API_CONFIG, HTTP_STATUS } from "../config/api";
import { logError, isRetryableError, createApiError } from "../utils/errorHandler";
import { showTooManyRequestsModal } from "../utils/notifications";

// Extended request config with custom options
export interface ApiRequestConfig extends AxiosRequestConfig {
  token?: string;
  noRetry?: boolean;
}

// Token management helpers
const getToken = (): string | null => {
  const authData = localStorage.getItem("auth-storage");
  if (authData) {
    try {
      const parsed = JSON.parse(authData);
      return parsed.state?.token || null;
    } catch {
      return null;
    }
  }
  return null;
};

const isExpiredToken = (token?: string): boolean => {
  if (!token) return true;
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000 < Date.now();
  } catch {
    return true;
  }
};

// Retry mechanism
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const retryRequest = async <T>(
  requestFn: () => Promise<T>,
  maxRetries: number = API_CONFIG.RETRY_ATTEMPTS,
  delay: number = API_CONFIG.RETRY_DELAY
): Promise<T> => {
  let lastError: AxiosError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error as AxiosError;
      
      if (attempt === maxRetries || !isRetryableError(lastError)) {
        throw lastError;
      }
      
      await sleep(delay * Math.pow(2, attempt)); // Exponential backoff
    }
  }
  
  throw lastError!;
};

// Create axios instance with interceptors
const createAxiosInstance = (options: { token?: string } = {}) => {
  const { token } = options;
  
  const instance = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
    },
  });

  // Request interceptor for logging
  instance.interceptors.request.use(
    (config) => {
      console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
      return config;
    },
    (error) => {
      logError(error, 'Request');
      return Promise.reject(error);
    }
  );

  // Response interceptor
  instance.interceptors.response.use(
    (response) => {
      const successStatuses = [HTTP_STATUS.OK, HTTP_STATUS.CREATED, HTTP_STATUS.NO_CONTENT];
      if (successStatuses.includes(response.status as any)) {
        return response.data;
      }
      return Promise.reject(response);
    },
    (error: AxiosError) => {
      logError(error, 'Response');

      // Handle 429 Too Many Requests
      if (error?.response?.status === HTTP_STATUS.TOO_MANY_REQUESTS) {
        const retryAfter = error?.response?.headers?.["retry-after"];
        const retryAfterSeconds = retryAfter ? parseInt(retryAfter, 10) : 60;
        showTooManyRequestsModal(retryAfterSeconds);
      }

      // Handle 401 Unauthorized - logout user
      if (error?.response?.status === HTTP_STATUS.UNAUTHORIZED) {
        const { logout } = useAuthStore.getState();
        logout();
      }

      // Transform error for consistent handling
      const apiError = createApiError(error);
      return Promise.reject(apiError);
    }
  );

  return instance;
};

// API Service Class
export class ApiService {
  /**
   * Login request (no retry for auth)
   */
  static async login<T>(url: string, data: any): Promise<T> {
    const api = createAxiosInstance();
    return api.post(url, data);
  }

  /**
   * Refresh token request (no retry for auth)
   */
  static async refreshToken<T>(url: string): Promise<T> {
    const token = getToken();
    const api = createAxiosInstance({ token: token || undefined });
    return api.post(url, {});
  }

  /**
   * Generic API request with automatic token handling and retry logic
   */
  static async request<T>(
    url: string,
    method: string = "get",
    data: any = {},
    options: ApiRequestConfig = {}
  ): Promise<T> {
    const { token, noRetry, ...axiosOptions } = options;
    
    // Auto-attach token if not provided and available
    let finalToken = token;
    if (!finalToken) {
      const storedToken = getToken();
      if (storedToken && !isExpiredToken(storedToken)) {
        finalToken = storedToken;
      }
    }

    const makeRequest = async (): Promise<T> => {
      const api = createAxiosInstance({ token: finalToken });
      
      switch (method.toLowerCase()) {
        case "get":
          return await api.get(url, axiosOptions);
        case "post":
          return await api.post(url, data, axiosOptions);
        case "put":
          return await api.put(url, data, axiosOptions);
        case "patch":
          return await api.patch(url, data, axiosOptions);
        case "delete":
          return await api.delete(url, axiosOptions);
        default:
          return await api.get(url, axiosOptions);
      }
    };

    // Use retry logic unless explicitly disabled
    if (noRetry) {
      return makeRequest();
    } else {
      return retryRequest(makeRequest);
    }
  }

  /**
   * GET request with retry
   */
  static async get<T>(url: string, options?: ApiRequestConfig): Promise<T> {
    return this.request<T>(url, "get", {}, options);
  }

  /**
   * POST request with retry
   */
  static async post<T>(url: string, data: any, options?: ApiRequestConfig): Promise<T> {
    return this.request<T>(url, "post", data, options);
  }

  /**
   * PUT request with retry
   */
  static async put<T>(url: string, data: any, options?: ApiRequestConfig): Promise<T> {
    return this.request<T>(url, "put", data, options);
  }

  /**
   * PATCH request with retry
   */
  static async patch<T>(url: string, data: any, options?: ApiRequestConfig): Promise<T> {
    return this.request<T>(url, "patch", data, options);
  }

  /**
   * DELETE request with retry
   */
  static async delete<T>(url: string, options?: ApiRequestConfig): Promise<T> {
    return this.request<T>(url, "delete", {}, options);
  }
}

// Legacy compatibility exports (matching your original structure)
export const apiLogin = ApiService.login;
export const apiRefreshToken = ApiService.refreshToken;
export const apiRequest = ApiService.request;