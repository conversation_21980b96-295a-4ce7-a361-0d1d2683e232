import { useEffect, useRef, useState } from 'react';

interface UseAutoRefreshOptions {
  interval?: number; // in milliseconds
  enabled?: boolean;
  onRefresh?: () => Promise<void> | void;
}

interface UseAutoRefreshReturn {
  isAutoRefreshing: boolean;
  timeUntilNextRefresh: number;
  toggleAutoRefresh: () => void;
  forceRefresh: () => Promise<void>;
  lastRefreshTime: Date | null;
}

export const useAutoRefresh = ({
  interval = 30000, // 30 seconds default
  enabled = false,
  onRefresh,
}: UseAutoRefreshOptions): UseAutoRefreshReturn => {
  const [isAutoRefreshing, setIsAutoRefreshing] = useState(enabled);
  const [timeUntilNextRefresh, setTimeUntilNextRefresh] = useState(interval);
  const [lastRefreshTime, setLastRefreshTime] = useState<Date | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);

  const clearIntervals = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      countdownRef.current = null;
    }
  };

  const performRefresh = async () => {
    if (isRefreshing || !onRefresh) return;
    
    setIsRefreshing(true);
    try {
      await onRefresh();
      setLastRefreshTime(new Date());
    } catch (error) {
      console.error('Auto-refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const forceRefresh = async () => {
    await performRefresh();
    // Reset countdown
    setTimeUntilNextRefresh(interval);
  };

  const toggleAutoRefresh = () => {
    setIsAutoRefreshing(prev => !prev);
  };

  useEffect(() => {
    if (isAutoRefreshing && onRefresh) {
      // Set up refresh interval
      intervalRef.current = setInterval(performRefresh, interval);
      
      // Set up countdown timer
      setTimeUntilNextRefresh(interval);
      countdownRef.current = setInterval(() => {
        setTimeUntilNextRefresh(prev => {
          if (prev <= 1000) {
            return interval; // Reset countdown
          }
          return prev - 1000;
        });
      }, 1000);
    } else {
      clearIntervals();
    }

    return clearIntervals;
  }, [isAutoRefreshing, interval, onRefresh]);

  // Cleanup on unmount
  useEffect(() => {
    return clearIntervals;
  }, []);

  return {
    isAutoRefreshing,
    timeUntilNextRefresh,
    toggleAutoRefresh,
    forceRefresh,
    lastRefreshTime,
  };
};
