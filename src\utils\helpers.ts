/**
 * Formats a timestamp to DD-MM-YYYY format
 * @param timestamp - Unix timestamp in seconds
 * @returns Formatted date string in DD-MM-YYYY format
 */
export function formatDate(timestamp: number): string {
  const date = new Date(timestamp * 1000); // Convert seconds to milliseconds
  
  if (isNaN(date.getTime())) {
    return "Invalid Date";
  }
  
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
  const year = date.getFullYear();
  
  return `${day}-${month}-${year}`;
}