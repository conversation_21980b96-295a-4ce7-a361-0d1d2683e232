import { useEffect, useState, useCallback } from "react";
import { Calendar, Users, QrCode, TrendingUp, BarChart3 } from "lucide-react";
import { useEventStore } from "../store/eventStore";
import AnalyticsWidget from "../components/AnalyticsWidget";
import DashboardStatsSkeleton from "../components/skeletons/DashboardStatsSkeleton";
import RecentEventsListSkeleton from "../components/skeletons/RecentEventsListSkeleton";
import DashboardErrorDisplay from "../components/DashboardErrorDisplay";
import { Link } from "react-router-dom";
import { EventDateStatus } from "../types";
import type { DashboardError } from "../types";
import { formatDate } from "../utils/helpers";
import { parseError, getRetryDelay } from "../utils/errorUtils";

const Dashboard = () => {
  const {
    events,
    isLoading,
    error: storeError,
    getEvents,
    shouldRefresh,
    setError,
  } = useEventStore();

  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isRetrying, setIsRetrying] = useState(false);
  const [dashboardError, setDashboardError] = useState<DashboardError | null>(
    null
  );

  // Retry function with exponential backoff
  const retryFetch = useCallback(
    async (attempt: number = 1) => {
      const maxRetries = 3;

      try {
        setIsRetrying(true);
        setDashboardError(null);
        setError(null);

        // Add delay for retries (not for first attempt)
        if (attempt > 1) {
          const delay = getRetryDelay(attempt - 1);
          await new Promise((resolve) => setTimeout(resolve, delay));
        }

        await getEvents();
      } catch (error) {
        console.error(
          `Failed to fetch dashboard data (attempt ${attempt}):`,
          error
        );
        const parsedError = parseError(error);

        if (attempt < maxRetries && parsedError.retryable) {
          await retryFetch(attempt + 1);
        } else {
          setDashboardError(parsedError);
        }
      } finally {
        setIsRetrying(false);
        setIsInitialLoad(false);
      }
    },
    [getEvents, setError]
  );

  // Manual retry function for user-triggered retries
  const handleRetry = useCallback(() => {
    retryFetch(1);
  }, [retryFetch]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Check if we need to refresh data or if this is initial load
        if (isInitialLoad || shouldRefresh()) {
          setDashboardError(null);
          setError(null);
          await retryFetch(1);
        }
      } catch {
        // Error handling is done in retryFetch
      }
    };

    fetchData();
  }, [isInitialLoad, shouldRefresh, retryFetch, setError]);

  // Calculate statistics from real API data
  const totalTickets = events.reduce(
    (sum, event) => sum + (event.total_ticket || 0),
    0
  );
  const totalCheckedIn = events.reduce(
    (sum, event) => sum + (event.total_checked_in || 0),
    0
  );
  const checkInRate =
    totalTickets > 0 ? (totalCheckedIn / totalTickets) * 100 : 0;

  // Debug logging to verify API data structure
  console.log("Dashboard Debug - Events data:", events);
  console.log("Dashboard Debug - Total tickets:", totalTickets);
  console.log("Dashboard Debug - Total checked in:", totalCheckedIn);

  const stats = [
    {
      name: "Total Events",
      value: events.length.toString(),
      icon: Calendar,
      change: "+2.1%",
      changeType: "positive" as const,
    },
    {
      name: "Total Tickets",
      value: totalTickets.toLocaleString(),
      icon: Users,
      change: "+15.3%",
      changeType: "positive" as const,
    },
    {
      name: "QR Codes Scanned",
      value: totalCheckedIn.toLocaleString(),
      icon: QrCode,
      change: "+8.2%",
      changeType: "positive" as const,
    },
    {
      name: "Check-in Rate",
      value: `${checkInRate.toFixed(1)}%`,
      icon: TrendingUp,
      change: "+3.1%",
      changeType: "positive" as const,
    },
  ];

  // Get recent events with real API data
  const recentEvents = events.slice(0, 3).map((eventStageDate) => {
    const event = eventStageDate.event_stage.event;
    return {
      id: event.id,
      name: event.name_vi,
      date: formatDate(eventStageDate.show_date),
      attendees: eventStageDate.total_ticket || 0,
      checkedIn: eventStageDate.total_checked_in || 0,
      status:
        eventStageDate.status === EventDateStatus.ONGOING
          ? "active"
          : eventStageDate.status === EventDateStatus.COMPLETED
          ? "completed"
          : "upcoming",
    };
  });

  // Determine the current error to display (dashboard error takes precedence)
  const currentError = dashboardError || storeError;

  // Show error state for critical failures during initial load
  if (isInitialLoad && currentError && !isRetrying) {
    return (
      <div className="space-y-4 sm:space-y-6">
        <div className="text-center sm:text-left">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
            Dashboard
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Welcome back! Here's what's happening with your events.
          </p>
        </div>
        <DashboardErrorDisplay
          error={currentError}
          onRetry={handleRetry}
          isRetrying={isRetrying}
          section="full"
        />
      </div>
    );
  }

  // Show loading state during initial load
  if (isInitialLoad && (isLoading || isRetrying)) {
    return (
      <div className="space-y-4 sm:space-y-6">
        <div className="text-center sm:text-left">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
            Dashboard
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Welcome back! Here's what's happening with your events.
          </p>
        </div>

        {/* Loading Stats Grid */}
        <DashboardStatsSkeleton />

        {/* Loading Recent Events */}
        <RecentEventsListSkeleton />

        {/* Loading Analytics Section */}
        <div className="bg-white shadow rounded-lg animate-pulse">
          <div className="px-3 py-4 sm:px-4 sm:py-5 lg:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-2 sm:space-y-0">
              <div className="h-5 sm:h-6 bg-gray-200 rounded w-32 sm:w-40" />
              <div className="h-4 bg-gray-200 rounded w-24 sm:w-32 self-start sm:self-auto" />
            </div>
            <div className="h-32 bg-gray-200 rounded" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="text-center sm:text-left">
        <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
          Dashboard
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          Welcome back! Here's what's happening with your events.
        </p>
      </div>

      {/* Stats Grid */}
      {isLoading && !isInitialLoad ? (
        <DashboardStatsSkeleton />
      ) : currentError && events.length === 0 ? (
        <DashboardErrorDisplay
          error={currentError}
          onRetry={handleRetry}
          isRetrying={isRetrying}
          section="stats"
        />
      ) : (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 sm:gap-5">
          {stats.map((stat) => {
            const Icon = stat.icon;
            return (
              <div
                key={stat.name}
                className="relative bg-white pt-4 px-3 pb-10 sm:pt-5 sm:px-4 sm:pb-12 lg:pt-6 lg:px-6 shadow rounded-lg overflow-hidden"
              >
                <dt>
                  <div className="absolute bg-blue-500 rounded-md p-2 sm:p-3">
                    <Icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                  </div>
                  <p className="ml-12 sm:ml-16 text-xs sm:text-sm font-medium text-gray-500 truncate">
                    {stat.name}
                  </p>
                </dt>
                <dd className="ml-12 sm:ml-16 pb-4 sm:pb-6 flex items-baseline sm:pb-7">
                  <p className="text-xl sm:text-2xl font-semibold text-gray-900">
                    {stat.value}
                  </p>
                  <p
                    className={`ml-1 sm:ml-2 flex items-baseline text-xs sm:text-sm font-semibold ${
                      stat.changeType === "positive"
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {stat.change}
                  </p>
                </dd>
              </div>
            );
          })}
        </div>
      )}

      {/* Recent Events */}
      {isLoading && !isInitialLoad ? (
        <RecentEventsListSkeleton />
      ) : currentError && events.length === 0 ? (
        <DashboardErrorDisplay
          error={currentError}
          onRetry={handleRetry}
          isRetrying={isRetrying}
          section="events"
        />
      ) : (
        <div className="bg-white shadow rounded-lg">
          <div className="px-3 py-4 sm:px-4 sm:py-5 lg:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-base sm:text-lg leading-6 font-medium text-gray-900">
                Recent Events
              </h3>
              {currentError && events.length > 0 && (
                <div className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
                  Using cached data
                </div>
              )}
            </div>
            <div className="mt-4 sm:mt-6 flow-root">
              {recentEvents.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-sm text-gray-500">No events found</p>
                  <p className="text-xs text-gray-400 mt-1">
                    Create your first event to get started
                  </p>
                </div>
              ) : (
                <ul className="-my-3 sm:-my-5 divide-y divide-gray-200">
                  {recentEvents.map((event) => (
                    <li key={event.id} className="py-3 sm:py-4">
                      <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {event.name}
                          </p>
                          <p className="text-xs sm:text-sm text-gray-500">
                            {event.date} • {event.attendees} attendees
                          </p>
                        </div>
                        <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2">
                          <span
                            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium self-start sm:self-auto ${
                              event.status === "active"
                                ? "bg-green-100 text-green-800"
                                : event.status === "completed"
                                ? "bg-gray-100 text-gray-800"
                                : "bg-blue-100 text-blue-800"
                            }`}
                          >
                            {event.status}
                          </span>
                          <div className="text-xs sm:text-sm text-gray-500">
                            {event.checkedIn}/{event.attendees} checked in
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Analytics Section */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-3 py-4 sm:px-4 sm:py-5 lg:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-2 sm:space-y-0">
            <h3 className="text-base sm:text-lg leading-6 font-medium text-gray-900 flex items-center">
              <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
              Quick Analytics
            </h3>
            <div className="flex items-center space-x-2">
              {currentError && events.length > 0 && (
                <div className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
                  Using cached data
                </div>
              )}
              <Link
                to="/analytics"
                className="text-xs sm:text-sm text-blue-600 hover:text-blue-800 font-medium self-start sm:self-auto"
              >
                View Full Analytics →
              </Link>
            </div>
          </div>
          {currentError && events.length === 0 ? (
            <DashboardErrorDisplay
              error={currentError}
              onRetry={handleRetry}
              isRetrying={isRetrying}
              section="analytics"
            />
          ) : (
            <AnalyticsWidget showCharts={false} />
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
