import { useState, useEffect, useCallback } from "react";
import { Calendar, MapPin, Users, BarChart3, Loader2 } from "lucide-react";
import BaseModal from "./BaseModal";
import { Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Cell } from "recharts";
import type { Event } from "../types";
import { getModalZIndex } from "../utils/zIndex";
import {
  DashboardService,
  type DashboardData,
} from "../services/dashboardService";

interface EventDashboardModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: Event | null;
  eventDateId?: string | null;
}

const EventDashboardModal = ({
  isOpen,
  onClose,
  event,
  eventDateId,
}: EventDashboardModalProps) => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = useCallback(async () => {
    if (!eventDateId) return;

    setIsLoading(true);
    setError(null);

    try {
      const data = await DashboardService.getDashboardData(eventDateId);
      setDashboardData(data);
    } catch (err) {
      console.error("Failed to fetch dashboard data:", err);
      setError("Failed to load dashboard data");
    } finally {
      setIsLoading(false);
    }
  }, [eventDateId]);

  // Fetch dashboard data when modal opens and eventDateId is available
  useEffect(() => {
    if (isOpen && eventDateId) {
      fetchDashboardData();
    }
  }, [isOpen, eventDateId, fetchDashboardData]);

  if (!isOpen || !event) return null;

  const formatDate = (timestamp: number) => {
    return new Intl.DateTimeFormat("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(timestamp * 1000));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  const getStatusColor = (status?: number) => {
    switch (status) {
      case 1:
        return "bg-green-100 text-green-800";
      case 0:
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Event Dashboard"
      subtitle={event.name_vi}
      icon={<BarChart3 className="h-6 w-6 text-blue-600" />}
      size="6xl"
      zIndex={getModalZIndex("EVENT_DASHBOARD_MODAL")}
    >
      {isLoading ? (
        <div className="text-center py-8 text-gray-500">
          <Loader2 className="h-12 w-12 mx-auto mb-2 text-gray-300 animate-spin" />
          <p>Loading dashboard data...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8 text-red-500">
          <BarChart3 className="h-12 w-12 mx-auto mb-2 text-red-300" />
          <p>{error}</p>
          <button
            onClick={fetchDashboardData}
            className="mt-2 text-sm text-blue-600 hover:text-blue-800"
          >
            Try again
          </button>
        </div>
      ) : !dashboardData ? (
        <div className="text-center py-8 text-gray-500">
          <BarChart3 className="h-12 w-12 mx-auto mb-2 text-gray-300" />
          <p>No dashboard data available</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Event Info */}
          <div className="lg:col-span-1 space-y-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-gray-900 mb-3">
                {dashboardData.eventDate.event_stage.event.name_vi}
              </h4>
              <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mb-3 ${getStatusColor(
                  dashboardData.eventDate.status
                )}`}
              >
                {dashboardData.eventDate.status === 1 ? "Active" : "Inactive"}
              </span>

              <div className="space-y-3">
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="h-4 w-4 mr-2" />
                  <div>
                    <div>
                      Start: {formatDate(dashboardData.eventDate.show_from)}
                    </div>
                    <div>
                      End: {formatDate(dashboardData.eventDate.show_to)}
                    </div>
                  </div>
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="h-4 w-4 mr-2" />
                  {dashboardData.eventDate.event_stage.venue_vi}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Users className="h-4 w-4 mr-2" />
                  {dashboardData.stats.totalTicketSold} sold /{" "}
                  {dashboardData.stats.totalTicketIssued} total tickets
                </div>
              </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-blue-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {dashboardData.stats.totalTicketIssued}
                </div>
                <div className="text-sm text-blue-600">Total Tickets</div>
              </div>
              <div className="bg-green-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-green-600">
                  {dashboardData.stats.totalTicketSold}
                </div>
                <div className="text-sm text-green-600">Tickets Sold</div>
              </div>
              <div className="bg-purple-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {dashboardData.stats.totalCheckedin}
                </div>
                <div className="text-sm text-purple-600">Checked In</div>
              </div>
              <div className="bg-orange-50 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {formatCurrency(dashboardData.stats.totalRevenue)}
                </div>
                <div className="text-sm text-orange-600">Total Revenue</div>
              </div>
            </div>
          </div>

          {/* Zone Statistics */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white border rounded-lg p-4">
              <h4 className="text-lg font-medium text-gray-900 mb-4">
                Zone Statistics
              </h4>
              <div className="space-y-4">
                {dashboardData.zoneStats.map((zone, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h5 className="font-medium text-gray-900">
                        {zone.zoneName}
                      </h5>
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: zone.color }}
                      ></div>
                    </div>
                    <div className="grid grid-cols-4 gap-4 text-sm">
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">
                          {zone.totalSeats}
                        </div>
                        <div className="text-gray-600">Total Seats</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">
                          {zone.soldSeats}
                        </div>
                        <div className="text-gray-600">Sold</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-purple-600">
                          {zone.checkedInSeats}
                        </div>
                        <div className="text-gray-600">Checked In</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-orange-600">
                          {formatCurrency(zone.revenue)}
                        </div>
                        <div className="text-gray-600">Revenue</div>
                      </div>
                    </div>
                    <div className="mt-3">
                      <div className="flex justify-between text-sm text-gray-600 mb-1">
                        <span>Sales Progress</span>
                        <span>
                          {zone.totalSeats > 0
                            ? Math.round(
                                (zone.soldSeats / zone.totalSeats) * 100
                              )
                            : 0}
                          %
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 rounded-full"
                          style={{
                            backgroundColor: zone.color,
                            width: `${
                              zone.totalSeats > 0
                                ? (zone.soldSeats / zone.totalSeats) * 100
                                : 0
                            }%`,
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Sales Overview */}
            <div className="bg-white border rounded-lg p-4">
              <h4 className="text-lg font-medium text-gray-900 mb-4">
                Sales Overview
              </h4>
              <div className="h-48">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        {
                          name: "Sold",
                          value: dashboardData.stats.totalTicketSold,
                          color: "#10B981",
                        },
                        {
                          name: "Available",
                          value:
                            dashboardData.stats.totalTicketIssued -
                            dashboardData.stats.totalTicketSold,
                          color: "#EF4444",
                        },
                      ]}
                      cx="50%"
                      cy="50%"
                      outerRadius={60}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, value }) => `${name}: ${value}`}
                    >
                      <Cell fill="#10B981" />
                      <Cell fill="#EF4444" />
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="flex justify-end mt-6">
        <button
          onClick={onClose}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Close
        </button>
      </div>
    </BaseModal>
  );
};

export default EventDashboardModal;
