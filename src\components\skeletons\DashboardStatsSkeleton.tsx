import React from 'react';

const DashboardStatsSkeleton: React.FC = () => {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 sm:gap-5">
      {[...Array(4)].map((_, index) => (
        <div
          key={index}
          className="relative bg-white pt-4 px-3 pb-10 sm:pt-5 sm:px-4 sm:pb-12 lg:pt-6 lg:px-6 shadow rounded-lg overflow-hidden animate-pulse transition-opacity duration-300 ease-in-out"
        >
          <dt>
            {/* Icon skeleton */}
            <div className="absolute bg-gray-200 rounded-md p-2 sm:p-3">
              <div className="h-5 w-5 sm:h-6 sm:w-6 bg-gray-300 rounded" />
            </div>
            {/* Label skeleton */}
            <div className="ml-12 sm:ml-16">
              <div className="h-3 sm:h-4 bg-gray-200 rounded w-20 sm:w-24" />
            </div>
          </dt>
          <dd className="ml-12 sm:ml-16 pb-4 sm:pb-6 flex items-baseline sm:pb-7 mt-2">
            {/* Value skeleton */}
            <div className="h-6 sm:h-8 bg-gray-200 rounded w-16 sm:w-20" />
            {/* Change percentage skeleton */}
            <div className="ml-1 sm:ml-2 h-3 sm:h-4 bg-gray-200 rounded w-10 sm:w-12" />
          </dd>
        </div>
      ))}
    </div>
  );
};

export default DashboardStatsSkeleton;