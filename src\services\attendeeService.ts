import { ApiService } from "./apiService";
import { API_ENDPOINTS } from "../config/api";
import type { Attendee, AttendeeFormData, CheckIn, ApiResponse } from "../types";

export interface CheckInResult {
  success: boolean;
  attendee: Attendee;
  checkIn: CheckIn;
  message?: string;
}

export class AttendeeService {
  /**
   * Get all attendees for a specific event
   */
  static async getEventAttendees(eventId: string): Promise<Attendee[]> {
    try {
      const response = await ApiService.get<ApiResponse<Attendee[]>>(
        API_ENDPOINTS.ATTENDEES.LIST(eventId)
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to fetch attendees");
    } catch (error) {
      console.error("Get attendees error:", error);
      throw error;
    }
  }

  /**
   * Get a specific attendee by ID
   */
  static async getAttendee(attendeeId: string): Promise<Attendee> {
    try {
      const response = await ApiService.get<ApiResponse<Attendee>>(
        API_ENDPOINTS.ATTENDEES.GET(attendeeId)
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to fetch attendee");
    } catch (error) {
      console.error("Get attendee error:", error);
      throw error;
    }
  }

  /**
   * Add a new attendee to an event
   */
  static async addAttendee(eventId: string, attendeeData: AttendeeFormData): Promise<Attendee> {
    try {
      const response = await ApiService.post<ApiResponse<Attendee>>(
        API_ENDPOINTS.ATTENDEES.CREATE(eventId),
        attendeeData
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to add attendee");
    } catch (error) {
      console.error("Add attendee error:", error);
      throw error;
    }
  }

  /**
   * Update attendee information
   */
  static async updateAttendee(
    attendeeId: string, 
    attendeeData: Partial<AttendeeFormData>
  ): Promise<Attendee> {
    try {
      const response = await ApiService.put<ApiResponse<Attendee>>(
        API_ENDPOINTS.ATTENDEES.UPDATE(attendeeId),
        attendeeData
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to update attendee");
    } catch (error) {
      console.error("Update attendee error:", error);
      throw error;
    }
  }

  /**
   * Remove an attendee from an event
   */
  static async removeAttendee(attendeeId: string): Promise<void> {
    try {
      const response = await ApiService.delete<ApiResponse<void>>(
        API_ENDPOINTS.ATTENDEES.DELETE(attendeeId)
      );
      
      if (response.status !== 200) {
        throw new Error(response.message || "Failed to remove attendee");
      }
    } catch (error) {
      console.error("Remove attendee error:", error);
      throw error;
    }
  }

  /**
   * Check in an attendee using QR code data
   */
  static async checkInAttendee(
    attendeeId: string, 
    eventId: string
  ): Promise<CheckInResult> {
    try {
      const response = await ApiService.post<ApiResponse<CheckInResult>>(
        API_ENDPOINTS.ATTENDEES.CHECK_IN(attendeeId),
        { eventId }
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to check in attendee");
    } catch (error) {
      console.error("Check in attendee error:", error);
      throw error;
    }
  }

  /**
   * Undo check-in for an attendee
   */
  static async undoCheckIn(attendeeId: string): Promise<Attendee> {
    try {
      const response = await ApiService.post<ApiResponse<Attendee>>(
        API_ENDPOINTS.ATTENDEES.UNDO_CHECK_IN(attendeeId),
        {}
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to undo check-in");
    } catch (error) {
      console.error("Undo check-in error:", error);
      throw error;
    }
  }

  /**
   * Get check-in history for an event
   */
  static async getCheckInHistory(eventId: string): Promise<CheckIn[]> {
    try {
      const response = await ApiService.get<ApiResponse<CheckIn[]>>(
        API_ENDPOINTS.CHECKINS.HISTORY(eventId)
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to fetch check-in history");
    } catch (error) {
      console.error("Get check-in history error:", error);
      throw error;
    }
  }

  /**
   * Bulk import attendees from CSV
   */
  static async bulkImportAttendees(
    eventId: string, 
    csvFile: File
  ): Promise<{ imported: number; errors: string[] }> {
    try {
      const formData = new FormData();
      formData.append('file', csvFile);

      const response = await ApiService.post<ApiResponse<{ imported: number; errors: string[] }>>(
        API_ENDPOINTS.ATTENDEES.IMPORT(eventId),
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to import attendees");
    } catch (error) {
      console.error("Bulk import error:", error);
      throw error;
    }
  }

  /**
   * Export attendees to CSV
   */
  static async exportAttendees(eventId: string): Promise<Blob> {
    try {
      const response = await ApiService.get<Blob>(
        API_ENDPOINTS.ATTENDEES.EXPORT(eventId),
        {
          responseType: 'blob',
        }
      );
      
      return response;
    } catch (error) {
      console.error("Export attendees error:", error);
      throw error;
    }
  }

  /**
   * Send QR codes to all attendees via email
   */
  static async sendQRCodes(eventId: string): Promise<{ sent: number; errors: string[] }> {
    try {
      const response = await ApiService.post<ApiResponse<{ sent: number; errors: string[] }>>(
        API_ENDPOINTS.ATTENDEES.SEND_QR_CODES(eventId),
        {}
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to send QR codes");
    } catch (error) {
      console.error("Send QR codes error:", error);
      throw error;
    }
  }

  /**
   * Resend QR code to a specific attendee
   */
  static async resendQRCode(attendeeId: string): Promise<void> {
    try {
      const response = await ApiService.post<ApiResponse<void>>(
        API_ENDPOINTS.ATTENDEES.RESEND_QR(attendeeId),
        {}
      );
      
      if (response.status !== 200) {
        throw new Error(response.message || "Failed to resend QR code");
      }
    } catch (error) {
      console.error("Resend QR code error:", error);
      throw error;
    }
  }
}