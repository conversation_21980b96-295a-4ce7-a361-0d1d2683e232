import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
} from "recharts";
import {
  TrendingUp,
  Users,
  Calendar,
  CheckCircle,
  BarChart3,
  AlertCircle,
  RefreshCw,
} from "lucide-react";
import { useEventStore } from "../store/eventStore";
import {
  AnalyticsService,
  type AnalyticsData,
} from "../services/analyticsService";
import AnalyticsWidgetSkeleton from "./skeletons/AnalyticsWidgetSkeleton";

interface AnalyticsWidgetProps {
  className?: string;
  showCharts?: boolean;
}

interface AnalyticsError {
  message: string;
  retryable: boolean;
}

const AnalyticsWidget = ({
  className = "",
  showCharts = true,
}: AnalyticsWidgetProps) => {
  const { events, attendees, isLoading, error } = useEventStore();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [analyticsError, setAnalyticsError] = useState<AnalyticsError | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  useEffect(() => {
    const calculateAnalytics = async () => {
      // Reset error state
      setAnalyticsError(null);
      
      // If store is loading, don't calculate yet
      if (isLoading) {
        return;
      }

      // If there's no data and no error, show empty state
      if (events.length === 0 && attendees.length === 0 && !error) {
        setAnalytics(null);
        return;
      }

      // If there's a store error, don't calculate
      if (error) {
        setAnalytics(null);
        return;
      }

      try {
        setIsCalculating(true);
        
        // Add a small delay to show loading state for better UX
        await new Promise(resolve => setTimeout(resolve, 100));
        
        const analyticsData = AnalyticsService.generateAnalytics(
          events,
          attendees
        );
        setAnalytics(analyticsData);
      } catch (err) {
        console.error('Failed to calculate analytics:', err);
        setAnalyticsError({
          message: 'Failed to calculate analytics data. Please try refreshing.',
          retryable: true,
        });
        setAnalytics(null);
      } finally {
        setIsCalculating(false);
      }
    };

    calculateAnalytics();
  }, [events, attendees, isLoading, error]);

  const handleRetryAnalytics = () => {
    setAnalyticsError(null);
    setIsCalculating(true);
    
    try {
      const analyticsData = AnalyticsService.generateAnalytics(
        events,
        attendees
      );
      setAnalytics(analyticsData);
    } catch (err) {
      console.error('Failed to retry analytics calculation:', err);
      setAnalyticsError({
        message: 'Failed to calculate analytics data. Please try refreshing.',
        retryable: true,
      });
    } finally {
      setIsCalculating(false);
    }
  };

  // Show skeleton loading state when store is loading or analytics are being calculated
  if (isLoading || isCalculating) {
    return <AnalyticsWidgetSkeleton className={className} showCharts={showCharts} />;
  }

  // Show error state for store errors
  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Unable to Load Analytics
          </h3>
          <p className="text-sm text-gray-500 mb-4">
            {error.message || 'There was an error loading the data needed for analytics.'}
          </p>
        </div>
      </div>
    );
  }

  // Show error state for analytics calculation errors
  if (analyticsError) {
    return (
      <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Analytics Error
          </h3>
          <p className="text-sm text-gray-500 mb-4">
            {analyticsError.message}
          </p>
          {analyticsError.retryable && (
            <button
              onClick={handleRetryAnalytics}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </button>
          )}
        </div>
      </div>
    );
  }

  // Show empty state when no data is available
  if (!analytics) {
    return (
      <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
        <div className="text-center py-8">
          <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Analytics Data
          </h3>
          <p className="text-sm text-gray-500">
            Analytics will appear here once you have events and attendees.
          </p>
        </div>
      </div>
    );
  }

  const COLORS = ["#3B82F6", "#10B981", "#F59E0B", "#EF4444"];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Quick Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-5 w-5 text-blue-500" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Events</p>
              <p className="text-lg font-semibold text-gray-900">
                {analytics.totalEvents}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-5 w-5 text-green-500" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Attendees</p>
              <p className="text-lg font-semibold text-gray-900">
                {analytics.totalAttendees}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-5 w-5 text-purple-500" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Check-ins</p>
              <p className="text-lg font-semibold text-gray-900">
                {analytics.totalCheckedIn}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-5 w-5 text-orange-500" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Rate</p>
              <p className="text-lg font-semibold text-gray-900">
                {analytics.overallCheckInRate.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts */}
      {showCharts && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Check-ins */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Recent Check-ins
            </h3>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={analytics.checkInTrends.slice(-5)}>
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Bar dataKey="checkIns" fill="#3B82F6" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Event Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Event Status
            </h3>
            <div className="h-48">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={analytics.eventStatusDistribution}
                    cx="50%"
                    cy="50%"
                    outerRadius={60}
                    fill="#8884d8"
                    dataKey="count"
                    label={({ status, percentage }) =>
                      `${status} ${percentage.toFixed(0)}%`
                    }
                  >
                    {analytics.eventStatusDistribution.map(
                      (_, index: number) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={COLORS[index % COLORS.length]}
                        />
                      )
                    )}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      )}

      {/* Top Events Summary */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Top Events</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {analytics.topEvents.slice(0, 3).map((item) => (
              <div
                key={item.event.id}
                className="flex items-center justify-between"
              >
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {item.event.event_stage?.event?.name_vi ||
                      item.event.short_desc_vi ||
                      "Unknown Event"}
                  </p>
                  <p className="text-sm text-gray-500 truncate">
                    {item.event.event_stage?.venue_vi || "Unknown Location"}
                  </p>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">
                      {item.attendeeCount}
                    </p>
                    <p className="text-xs text-gray-500">Attendees</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">
                      {item.checkInCount}
                    </p>
                    <p className="text-xs text-gray-500">Check-ins</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">
                      {item.checkInRate.toFixed(1)}%
                    </p>
                    <p className="text-xs text-gray-500">Rate</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsWidget;
