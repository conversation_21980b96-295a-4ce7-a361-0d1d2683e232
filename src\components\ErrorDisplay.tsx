import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, Wifi, WifiOff } from "lucide-react";
import type { DashboardError } from "../types";
import { DashboardErrorType } from "../types";

interface ErrorDisplayProps {
  error: DashboardError;
  onRetry?: () => void;
  isRetrying?: boolean;
  className?: string;
}

const ErrorDisplay = ({ error, onRetry, isRetrying = false, className = "" }: ErrorDisplayProps) => {
  const getErrorIcon = () => {
    switch (error.type) {
      case DashboardErrorType.NETWORK_ERROR:
        return <WifiOff className="h-8 w-8 text-red-500" />;
      case DashboardErrorType.TIMEOUT_ERROR:
        return <Wifi className="h-8 w-8 text-orange-500" />;
      default:
        return <AlertCircle className="h-8 w-8 text-red-500" />;
    }
  };

  const getErrorTitle = () => {
    switch (error.type) {
      case DashboardErrorType.NETWORK_ERROR:
        return "Connection Problem";
      case DashboardErrorType.API_ERROR:
        return "Server Error";
      case DashboardErrorType.TIMEOUT_ERROR:
        return "Request Timeout";
      default:
        return "Something Went Wrong";
    }
  };

  const getErrorDescription = () => {
    switch (error.type) {
      case DashboardErrorType.NETWORK_ERROR:
        return "Please check your internet connection and try again.";
      case DashboardErrorType.API_ERROR:
        return error.statusCode 
          ? `Server returned error ${error.statusCode}. Please try again later.`
          : "The server encountered an error. Please try again later.";
      case DashboardErrorType.TIMEOUT_ERROR:
        return "The request took too long to complete. Please try again.";
      default:
        return "An unexpected error occurred. Please try again.";
    }
  };

  return (
    <div className={`text-center py-8 px-4 ${className}`}>
      <div className="flex justify-center mb-4">
        {getErrorIcon()}
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        {getErrorTitle()}
      </h3>
      <p className="text-sm text-gray-500 mb-4 max-w-md mx-auto">
        {getErrorDescription()}
      </p>
      <p className="text-xs text-gray-400 mb-6">
        {error.message}
      </p>
      {error.retryable && onRetry && (
        <button
          onClick={onRetry}
          disabled={isRetrying}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
          {isRetrying ? 'Retrying...' : 'Try Again'}
        </button>
      )}
    </div>
  );
};

export default ErrorDisplay;