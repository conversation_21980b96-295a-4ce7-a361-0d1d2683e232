import { EventDateStatus, type Event, type EventListItem } from "../types";

/**
 * Transform Event data to include computed properties for component compatibility
 */
export const transformEvent = (event: Event): Event => {
  return {
    ...event,
    name: event.name_en || event.name_vi || "Untitled Event",
    description: event.content_en || event.content_vi || "",
    startDate: new Date(event.showdate_from * 1000),
    endDate: new Date(event.showdate_to * 1000),
    location: event.venue_en || event.venue_vi || "",
    maxAttendees: undefined, // Not available in current schema
    status: event.active ? 1 : 0, // Map active status to numeric
  };
};

/**
 * Transform EventListItem to Event for component compatibility
 */
export const transformEventListItem = (eventListItem: EventListItem): Event => {
  const baseEvent = eventListItem.event_stage?.event;
  if (!baseEvent) {
    throw new Error("EventListItem missing event data");
  }

  return transformEvent({
    ...baseEvent,
    // Override with EventListItem specific data where applicable
    active: eventListItem.active,
  });
};

/**
 * Get event status text
 */
export const getEventStatusText = (status: number): string => {
  switch (status) {
    case 1:
      return "Active";
    case 0:
      return "Inactive";
    default:
      return "Unknown";
  }
};

/**
 * Check if event is currently ongoing
 */
export const isEventOngoing = (event: EventListItem): boolean => {
  return event.status === EventDateStatus.ONGOING;
};

/**
 * Check if event is upcoming
 */
export const isEventUpcoming = (event: EventListItem): boolean => {
  return event.status === EventDateStatus.UPCOMING;
};

/**
 * Check if event is completed
 */
export const isEventCompleted = (event: EventListItem): boolean => {
  return event.status === EventDateStatus.COMPLETED;
};
