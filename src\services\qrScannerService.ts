import QrScanner from "qr-scanner";
import { QRCodeService } from "./qrCodeService";
import type { QRCodeData } from "../types";

export interface ScanResult {
  success: boolean;
  data?: QRCodeData;
  rawData?: string; // Raw scanned data (for URLs like Solana addresses)
  scanType?: "encrypted" | "url" | "unknown";
  error?: string;
  attendeeId?: string;
  eventId?: string;
}

export interface CameraDevice {
  id: string;
  label: string;
}

export class QRScannerService {
  private static scanner: QrScanner | null = null;

  private static isScanning = false;
  private static onScanCallback: ((result: ScanResult) => void) | null = null;

  /**
   * Initialize the QR scanner with camera
   */
  static async initializeScanner(
    videoElement: HTMLVideoElement,
    onScan: (result: ScanResult) => void,
    preferredCamera?: string
  ): Promise<void> {
    try {
      this.onScanCallback = onScan;

      // Check if QR Scanner is supported
      if (!QrScanner.hasCamera()) {
        throw new Error("No camera available on this device");
      }

      // Create scanner instance
      this.scanner = new QrScanner(
        videoElement,
        (result) => this.handleScanResult(result.data),
        {
          returnDetailedScanResult: true,
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: preferredCamera || "environment", // Use back camera by default
        }
      );

      // Start scanning
      await this.scanner.start();
      this.isScanning = true;

      console.log("QR Scanner initialized successfully");
    } catch (error) {
      console.error("Failed to initialize QR scanner:", error);
      throw new Error(
        `Camera initialization failed: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Handle scan result and validate QR code
   */
  private static handleScanResult(data: string): void {
    if (!this.onScanCallback) return;

    try {
      console.log("--qrScannerService-----handleScanResult-----", data);

      // Check if this is a URL (like Solana address)
      if (this.isUrl(data)) {
        this.onScanCallback({
          success: true,
          rawData: data,
          scanType: "url",
        });
        return;
      }

      // Try to validate and decrypt as traditional QR code
      const validation = QRCodeService.validateQRCode(data);

      if (validation.isValid && validation.qrData) {
        this.onScanCallback({
          success: true,
          data: validation.qrData,
          rawData: data,
          scanType: "encrypted",
          attendeeId: validation.qrData.attendeeId,
          eventId: validation.qrData.eventId,
        });
      } else {
        // If not a valid encrypted QR code, treat as unknown raw data
        this.onScanCallback({
          success: true,
          rawData: data,
          scanType: "unknown",
        });
      }
    } catch (error) {
      this.onScanCallback({
        success: false,
        rawData: data,
        scanType: "unknown",
        error:
          error instanceof Error ? error.message : "Failed to process QR code",
      });
    }
  }

  /**
   * Check if the scanned data is a URL
   */
  private static isUrl(data: string): boolean {
    try {
      new URL(data);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Stop scanning and release camera
   */
  static stopScanner(): void {
    if (this.scanner) {
      this.scanner.stop();
      this.scanner.destroy();
      this.scanner = null;
    }
    this.isScanning = false;

    this.onScanCallback = null;
    console.log("QR Scanner stopped");
  }

  /**
   * Pause scanning temporarily
   */
  static pauseScanner(): void {
    if (this.scanner && this.isScanning) {
      this.scanner.stop();
      this.isScanning = false;
    }
  }

  /**
   * Resume scanning
   */
  static async resumeScanner(): Promise<void> {
    if (this.scanner && !this.isScanning) {
      await this.scanner.start();
      this.isScanning = true;
    }
  }

  /**
   * Get available cameras
   */
  static async getAvailableCameras(): Promise<CameraDevice[]> {
    try {
      const cameras = await QrScanner.listCameras(true);
      return cameras.map((camera) => ({
        id: camera.id,
        label: camera.label || `Camera ${camera.id}`,
      }));
    } catch (error) {
      console.error("Failed to get cameras:", error);
      return [];
    }
  }

  /**
   * Switch to a different camera
   */
  static async switchCamera(cameraId: string): Promise<void> {
    if (this.scanner) {
      await this.scanner.setCamera(cameraId);
    }
  }

  /**
   * Check if scanner is currently active
   */
  static isActive(): boolean {
    return this.isScanning;
  }

  /**
   * Get current scanner state
   */
  static async getState(): Promise<{
    isScanning: boolean;
    hasScanner: boolean;
    hasCamera: boolean;
  }> {
    return {
      isScanning: this.isScanning,
      hasScanner: this.scanner !== null,
      hasCamera: await QrScanner.hasCamera(),
    };
  }

  /**
   * Request camera permissions
   */
  static async requestCameraPermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: "environment" },
      });

      // Stop the stream immediately as we just wanted to check permissions
      stream.getTracks().forEach((track) => track.stop());

      return true;
    } catch (error) {
      console.error("Camera permission denied:", error);
      return false;
    }
  }

  /**
   * Scan QR code from image file
   */
  static async scanFromFile(file: File): Promise<ScanResult> {
    try {
      const result = await QrScanner.scanImage(file, {
        returnDetailedScanResult: true,
      });
      console.log("----qrScannerService----scanFromFile--", result);

      const data = result.data;

      // Check if this is a URL (like Solana address)
      if (this.isUrl(data)) {
        return {
          success: true,
          rawData: data,
          scanType: "url",
        };
      }

      // Try to validate and decrypt as traditional QR code
      const validation = QRCodeService.validateQRCode(data);

      if (validation.isValid && validation.qrData) {
        return {
          success: true,
          data: validation.qrData,
          rawData: data,
          scanType: "encrypted",
          attendeeId: validation.qrData.attendeeId,
          eventId: validation.qrData.eventId,
        };
      } else {
        // If not a valid encrypted QR code, treat as unknown raw data
        return {
          success: true,
          rawData: data,
          scanType: "unknown",
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to scan image",
      };
    }
  }

  /**
   * Enable/disable torch (flashlight) if available
   */
  static async toggleTorch(): Promise<boolean> {
    if (this.scanner) {
      try {
        await this.scanner.toggleFlash();
        return true;
      } catch (error) {
        console.error("Failed to toggle torch:", error);
        return false;
      }
    }
    return false;
  }

  /**
   * Check if torch is available
   */
  static async hasTorch(): Promise<boolean> {
    if (this.scanner) {
      try {
        return await this.scanner.hasFlash();
      } catch (error) {
        return false;
      }
    }
    return false;
  }

  /**
   * Get scanner capabilities
   */
  static async getCapabilities(): Promise<{
    hasCamera: boolean;
    hasTorch: boolean;
    cameras: CameraDevice[];
  }> {
    const hasCamera = await QrScanner.hasCamera();
    const cameras = hasCamera ? await this.getAvailableCameras() : [];
    const hasTorch = hasCamera && this.scanner ? await this.hasTorch() : false;

    return {
      hasCamera,
      hasTorch,
      cameras,
    };
  }
}
