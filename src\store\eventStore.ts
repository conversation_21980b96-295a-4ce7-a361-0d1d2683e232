import { create } from "zustand";
import { persist } from "zustand/middleware";
import type {
  Event,
  Attendee,
  AttendeeFormData,
  EventListItem,
  DashboardError,
} from "../types";
import { QRCodeService } from "../services/qrCodeService";
import { EventService } from "../services/eventService";
import { parseError } from "../utils/errorUtils";

interface EventState {
  events: EventListItem[];
  attendees: Attendee[];
  selectedEvent: Event | null;
  isLoading: boolean;
  error: DashboardError | null;
  lastFetchTime: number | null;

  // Event actions
  getEvents: () => Promise<EventListItem[] | undefined>;
  getEvent: (id: string) => EventListItem | undefined;
  setSelectedEvent: (event: Event | null) => void;

  // Attendee actions
  addAttendee: (
    eventId: string,
    attendeeData: AttendeeFormData
  ) => Promise<Attendee>;
  removeAttendee: (attendeeId: string) => Promise<void>;
  getEventAttendees: (eventId: string) => Attendee[];
  checkInAttendee: (attendeeId: string) => Promise<void>;

  // Utility actions
  setLoading: (loading: boolean) => void;
  setError: (error: DashboardError | null) => void;
  shouldRefresh: () => boolean;
  forceRefresh: () => Promise<void>;
  initializeMockData: () => void;
}

// This function is no longer needed as we're using real API data

// Mock attendees for demonstration - replace with real API data when available
const generateMockAttendees = (): Attendee[] => [];

// Helper function to ensure dates are Date objects
const ensureDateObjects = (data: any): any => {
  if (Array.isArray(data)) {
    return data.map(ensureDateObjects);
  } else if (data && typeof data === "object") {
    const result: any = {};
    for (const [key, value] of Object.entries(data)) {
      if (key.includes("Date") || key.includes("At")) {
        result[key] = value ? new Date(value as string) : value;
      } else {
        result[key] = ensureDateObjects(value);
      }
    }
    return result;
  }
  return data;
};

export const useEventStore = create<EventState>()(
  persist(
    (set, get) => ({
      events: [],
      attendees: [],
      selectedEvent: null,
      isLoading: false,
      error: null,
      lastFetchTime: null,

      deleteEvent: async (id: string) => {
        set({ isLoading: true, error: null });
        try {
          await EventService.deleteEvent(id);
          set((state) => ({
            events: state.events.filter((event) => event.id !== id),
            attendees: state.attendees.filter(
              (attendee) => attendee.eventId !== id
            ),
            selectedEvent:
              state.selectedEvent?.id === id ? null : state.selectedEvent,
            isLoading: false,
            error: null,
          }));
        } catch (error) {
          const parsedError = parseError(error);
          set({ isLoading: false, error: parsedError });
          throw error;
        }
      },

      getEvents: async () => {
        set({ isLoading: true, error: null });
        try {
          const response = await EventService.getEvents();
          set({
            events: response,
            isLoading: false,
            error: null,
            lastFetchTime: Date.now(),
          });
          return response;
        } catch (error) {
          console.error('Failed to fetch events:', error);
          const parsedError = parseError(error);
          set({ 
            isLoading: false, 
            error: parsedError 
          });
          throw error;
        }
      },

      getEvent: (id: string) => {
        return get().events.find((event) => event.id === id);
      },

      setSelectedEvent: (event: Event | null) => {
        set({ selectedEvent: event });
      },

      addAttendee: async (eventId: string, attendeeData: AttendeeFormData) => {
        set({ isLoading: true, error: null });

        try {
          await new Promise((resolve) => setTimeout(resolve, 300));

          const attendeeId = Date.now().toString();
          const qrCodeId = QRCodeService.generateQRCodeId(attendeeId, eventId);

          const newAttendee: Attendee = {
            id: attendeeId,
            eventId,
            ...attendeeData,
            qrCode: qrCodeId,
            registeredAt: new Date(),
            checkedIn: false,
          };

          set((state) => ({
            attendees: [...state.attendees, newAttendee],
            isLoading: false,
            error: null,
          }));

          return newAttendee;
        } catch (error) {
          const parsedError = parseError(error);
          set({ isLoading: false, error: parsedError });
          throw error;
        }
      },

      removeAttendee: async (attendeeId: string) => {
        set({ isLoading: true, error: null });

        try {
          await new Promise((resolve) => setTimeout(resolve, 200));

          set((state) => ({
            attendees: state.attendees.filter(
              (attendee) => attendee.id !== attendeeId
            ),
            isLoading: false,
            error: null,
          }));
        } catch (error) {
          const parsedError = parseError(error);
          set({ isLoading: false, error: parsedError });
          throw error;
        }
      },

      getEventAttendees: (eventId: string) => {
        return get().attendees.filter(
          (attendee) => attendee.eventId === eventId
        );
      },

      checkInAttendee: async (attendeeId: string) => {
        set({ isLoading: true, error: null });

        try {
          await new Promise((resolve) => setTimeout(resolve, 200));

          set((state) => ({
            attendees: state.attendees.map((attendee) =>
              attendee.id === attendeeId
                ? {
                  ...attendee,
                  checkedIn: true,
                  checkedInAt: new Date(),
                }
                : attendee
            ),
            isLoading: false,
            error: null,
          }));
        } catch (error) {
          const parsedError = parseError(error);
          set({ isLoading: false, error: parsedError });
          throw error;
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: DashboardError | null) => {
        set({ error });
      },

      shouldRefresh: () => {
        const { lastFetchTime } = get();
        if (!lastFetchTime) return true;
        
        // Refresh if data is older than 5 minutes (300,000 ms)
        const REFRESH_THRESHOLD = 5 * 60 * 1000;
        return Date.now() - lastFetchTime > REFRESH_THRESHOLD;
      },

      forceRefresh: async () => {
        const { getEvents } = get();
        await getEvents();
      },

      initializeMockData: () => {
        set({ 
          events: [], 
          attendees: generateMockAttendees() 
        });
      },
    }),
    {
      name: "event-storage",
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Events from API don't need date conversion as they use Unix timestamps
          state.attendees = ensureDateObjects(state.attendees);
          state.selectedEvent = state.selectedEvent
            ? ensureDateObjects(state.selectedEvent)
            : null;
        }
      },
    }
  )
);
