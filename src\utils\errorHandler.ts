import type { AxiosError } from "axios";
import { HTTP_STATUS, ERROR_MESSAGES } from "../config/api";

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

/**
 * Extract error message from axios error
 */
export const extractErrorMessage = (error: AxiosError): string => {
  // Check for custom error message from API
  const responseData = error.response?.data as any;
  const apiMessage = responseData?.error?.message || responseData?.message;
  if (apiMessage) return apiMessage;

  // Handle specific HTTP status codes
  switch (error.response?.status) {
    case HTTP_STATUS.BAD_REQUEST:
      return ERROR_MESSAGES.VALIDATION_ERROR;
    case HTTP_STATUS.UNAUTHORIZED:
      return ERROR_MESSAGES.UNAUTHORIZED;
    case HTTP_STATUS.FORBIDDEN:
      return ERROR_MESSAGES.FORBIDDEN;
    case HTTP_STATUS.NOT_FOUND:
      return ERROR_MESSAGES.NOT_FOUND;
    case HTTP_STATUS.TOO_MANY_REQUESTS:
      return ERROR_MESSAGES.RATE_LIMITED;
    case HTTP_STATUS.INTERNAL_SERVER_ERROR:
      return ERROR_MESSAGES.SERVER_ERROR;
    default:
      return error.message || ERROR_MESSAGES.UNKNOWN_ERROR;
  }
};

/**
 * Create standardized API error object
 */
export const createApiError = (error: AxiosError): ApiError => {
  const responseData = error.response?.data as any;
  return {
    message: extractErrorMessage(error),
    code: responseData?.error?.code || error.code,
    status: error.response?.status,
    details: error.response?.data,
  };
};

/**
 * Check if error is a network error
 */
export const isNetworkError = (error: AxiosError): boolean => {
  return !error.response && error.code !== "ECONNABORTED";
};

/**
 * Check if error is a timeout error
 */
export const isTimeoutError = (error: AxiosError): boolean => {
  return error.code === "ECONNABORTED";
};

/**
 * Check if error is retryable
 */
export const isRetryableError = (error: AxiosError): boolean => {
  if (isNetworkError(error) || isTimeoutError(error)) return true;

  const status = error.response?.status;
  return Boolean(
    status === HTTP_STATUS.TOO_MANY_REQUESTS ||
      status === HTTP_STATUS.INTERNAL_SERVER_ERROR ||
      (status && status >= 502 && status <= 504)
  );
};

/**
 * Log error for debugging
 */
export const logError = (error: AxiosError, context?: string): void => {
  const apiError = createApiError(error);
  console.error(`API Error${context ? ` (${context})` : ""}:`, {
    message: apiError.message,
    status: apiError.status,
    code: apiError.code,
    url: error.config?.url,
    method: error.config?.method?.toUpperCase(),
    details: apiError.details,
  });
};
