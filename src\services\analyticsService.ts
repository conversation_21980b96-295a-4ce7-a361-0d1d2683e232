import type { Attendee, EventStats, EventListItem } from "../types";
import {
  format,
  startOfDay,
  endOfDay,
  subDays,
  eachHourOfInterval,
  eachDayOfInterval,
} from "date-fns";

export interface AnalyticsData {
  totalEvents: number;
  totalAttendees: number;
  totalCheckedIn: number;
  overallCheckInRate: number;
  eventsThisMonth: number;
  attendeesThisMonth: number;
  checkInsToday: number;
  topEvents: Array<{
    event: EventListItem;
    attendeeCount: number;
    checkInCount: number;
    checkInRate: number;
  }>;
  checkInTrends: Array<{
    date: string;
    checkIns: number;
    registrations: number;
  }>;
  hourlyCheckIns: Array<{
    hour: string;
    count: number;
  }>;
  eventStatusDistribution: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  attendeeGrowth: Array<{
    date: string;
    cumulative: number;
    daily: number;
  }>;
}

export interface ExportData {
  events: EventListItem[];
  attendees: Attendee[];
  summary: {
    totalEvents: number;
    totalAttendees: number;
    totalCheckedIn: number;
    checkInRate: number;
    generatedAt: string;
  };
}

export class AnalyticsService {
  /**
   * Generate comprehensive analytics data
   */
  static generateAnalytics(
    events: EventListItem[],
    attendees: Attendee[]
  ): AnalyticsData {
    try {
      // Validate input data
      if (!Array.isArray(events)) {
        throw new Error('Events data is invalid');
      }
      if (!Array.isArray(attendees)) {
        throw new Error('Attendees data is invalid');
      }

      const now = new Date();
      const startOfToday = startOfDay(now);
      const endOfToday = endOfDay(now);
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const last7Days = subDays(now, 7);

      // Basic statistics
      const totalEvents = events.length;
      const totalAttendees = attendees.length;
      const totalCheckedIn = attendees.filter((a) => a.checkedIn).length;
      const overallCheckInRate =
        totalAttendees > 0 ? (totalCheckedIn / totalAttendees) * 100 : 0;

      // Monthly statistics with error handling
      const eventsThisMonth = events.filter((e) => {
        try {
          return new Date(e.created_at) >= startOfMonth;
        } catch {
          return false;
        }
      }).length;
      
      const attendeesThisMonth = attendees.filter((a) => {
        try {
          return a.registeredAt && new Date(a.registeredAt) >= startOfMonth;
        } catch {
          return false;
        }
      }).length;

      // Today's check-ins with error handling
      const checkInsToday = attendees.filter((a) => {
        try {
          return (
            a.checkedIn &&
            a.checkedInAt &&
            new Date(a.checkedInAt) >= startOfToday &&
            new Date(a.checkedInAt) <= endOfToday
          );
        } catch {
          return false;
        }
      }).length;

      // Top events by attendance and check-in rate with error handling
      const topEvents = events
        .map((event) => {
          try {
            const eventAttendees = attendees.filter((a) => a.eventId === event.id);
            const checkedInCount = eventAttendees.filter((a) => a.checkedIn).length;
            const checkInRate =
              eventAttendees.length > 0
                ? (checkedInCount / eventAttendees.length) * 100
                : 0;

            return {
              event,
              attendeeCount: eventAttendees.length,
              checkInCount: checkedInCount,
              checkInRate,
            };
          } catch (error) {
            console.warn('Error processing event for analytics:', event.id, error);
            return {
              event,
              attendeeCount: 0,
              checkInCount: 0,
              checkInRate: 0,
            };
          }
        })
        .sort((a, b) => b.attendeeCount - a.attendeeCount)
        .slice(0, 5);

      // Check-in trends over last 7 days with error handling
      const checkInTrends = eachDayOfInterval({ start: last7Days, end: now }).map(
        (date) => {
          try {
            const dayStart = startOfDay(date);
            const dayEnd = endOfDay(date);

            const checkIns = attendees.filter((a) => {
              try {
                return (
                  a.checkedIn &&
                  a.checkedInAt &&
                  new Date(a.checkedInAt) >= dayStart &&
                  new Date(a.checkedInAt) <= dayEnd
                );
              } catch {
                return false;
              }
            }).length;

            const registrations = attendees.filter((a) => {
              try {
                return (
                  a.registeredAt &&
                  new Date(a.registeredAt) >= dayStart &&
                  new Date(a.registeredAt) <= dayEnd
                );
              } catch {
                return false;
              }
            }).length;

            return {
              date: format(date, "MMM dd"),
              checkIns,
              registrations,
            };
          } catch (error) {
            console.warn('Error processing check-in trends for date:', date, error);
            return {
              date: format(date, "MMM dd"),
              checkIns: 0,
              registrations: 0,
            };
          }
        }
      );

      // Hourly check-ins for today with error handling
      const hourlyCheckIns = eachHourOfInterval({
        start: startOfToday,
        end: endOfToday,
      }).map((hour) => {
        try {
          const hourEnd = new Date(hour.getTime() + 60 * 60 * 1000);
          const count = attendees.filter((a) => {
            try {
              return (
                a.checkedIn &&
                a.checkedInAt &&
                new Date(a.checkedInAt) >= hour &&
                new Date(a.checkedInAt) < hourEnd
              );
            } catch {
              return false;
            }
          }).length;

          return {
            hour: format(hour, "HH:mm"),
            count,
          };
        } catch (error) {
          console.warn('Error processing hourly check-ins for hour:', hour, error);
          return {
            hour: format(hour, "HH:mm"),
            count: 0,
          };
        }
      });

      // Event status distribution with error handling
      const statusCounts = events.reduce((acc, event) => {
        try {
          const status = event.status || 'active';
          acc[status] = (acc[status] || 0) + 1;
          return acc;
        } catch (error) {
          console.warn('Error processing event status:', event.id, error);
          acc['unknown'] = (acc['unknown'] || 0) + 1;
          return acc;
        }
      }, {} as Record<string, number>);

      const eventStatusDistribution = Object.entries(statusCounts).map(
        ([status, count]) => ({
          status: status.charAt(0).toUpperCase() + status.slice(1),
          count,
          percentage: totalEvents > 0 ? (count / totalEvents) * 100 : 0,
        })
      );

      // Attendee growth over last 30 days with error handling
      const last30Days = subDays(now, 30);
      const attendeeGrowth = eachDayOfInterval({
        start: last30Days,
        end: now,
      }).map((date) => {
        try {
          const dayStart = startOfDay(date);
          const dayEnd = endOfDay(date);

          const daily = attendees.filter((a) => {
            try {
              return (
                a.registeredAt &&
                new Date(a.registeredAt) >= dayStart &&
                new Date(a.registeredAt) <= dayEnd
              );
            } catch {
              return false;
            }
          }).length;

          const cumulative = attendees.filter((a) => {
            try {
              return a.registeredAt && new Date(a.registeredAt) <= dayEnd;
            } catch {
              return false;
            }
          }).length;

          return {
            date: format(date, "MMM dd"),
            cumulative,
            daily,
          };
        } catch (error) {
          console.warn('Error processing attendee growth for date:', date, error);
          return {
            date: format(date, "MMM dd"),
            cumulative: 0,
            daily: 0,
          };
        }
      });

      return {
        totalEvents,
        totalAttendees,
        totalCheckedIn,
        overallCheckInRate,
        eventsThisMonth,
        attendeesThisMonth,
        checkInsToday,
        topEvents,
        checkInTrends,
        hourlyCheckIns,
        eventStatusDistribution,
        attendeeGrowth,
      };
    } catch (error) {
      console.error('Error generating analytics data:', error);
      throw new Error('Failed to generate analytics data');
    }
  }

  /**
   * Generate event-specific statistics
   */
  static generateEventStats(event: EventListItem, attendees: Attendee[]): EventStats {
    try {
      // Validate input data
      if (!event || !Array.isArray(attendees)) {
        throw new Error('Invalid event or attendees data');
      }

      const eventAttendees = attendees.filter((a) => a.eventId === event.id);
      const checkedInCount = eventAttendees.filter((a) => a.checkedIn).length;
      const checkInRate =
        eventAttendees.length > 0
          ? (checkedInCount / eventAttendees.length) * 100
          : 0;

      // Hourly check-ins for the event day with error handling
      let hourlyCheckIns: Array<{ hour: string; count: number }> = [];
      
      try {
        const eventDate = new Date(event.show_date * 1000); // Convert Unix timestamp to Date
        const eventStart = startOfDay(eventDate);
        const eventEnd = endOfDay(eventDate);

        hourlyCheckIns = eachHourOfInterval({
          start: eventStart,
          end: eventEnd,
        }).map((hour) => {
          try {
            const hourEnd = new Date(hour.getTime() + 60 * 60 * 1000);
            const count = eventAttendees.filter((a) => {
              try {
                return (
                  a.checkedIn &&
                  a.checkedInAt &&
                  new Date(a.checkedInAt) >= hour &&
                  new Date(a.checkedInAt) < hourEnd
                );
              } catch {
                return false;
              }
            }).length;

            return {
              hour: format(hour, "HH:mm"),
              count,
            };
          } catch (error) {
            console.warn('Error processing hourly check-ins for hour:', hour, error);
            return {
              hour: format(hour, "HH:mm"),
              count: 0,
            };
          }
        });
      } catch (error) {
        console.warn('Error processing event date for hourly check-ins:', error);
        // Provide empty hourly data if date processing fails
        hourlyCheckIns = [];
      }

      return {
        totalAttendees: eventAttendees.length,
        checkedInCount,
        checkInRate,
        hourlyCheckIns,
      };
    } catch (error) {
      console.error('Error generating event stats:', error);
      throw new Error('Failed to generate event statistics');
    }
  }

  /**
   * Export data to CSV format
   */
  static exportToCSV(events: EventListItem[], attendees: Attendee[]): string {
    const analytics = this.generateAnalytics(events, attendees);

    let csv = "Event Management Analytics Report\n\n";
    csv += `Generated: ${new Date().toLocaleString()}\n\n`;

    // Summary statistics
    csv += "SUMMARY STATISTICS\n";
    csv += "Metric,Value\n";
    csv += `Total Events,${analytics.totalEvents}\n`;
    csv += `Total Attendees,${analytics.totalAttendees}\n`;
    csv += `Total Check-ins,${analytics.totalCheckedIn}\n`;
    csv += `Overall Check-in Rate,${analytics.overallCheckInRate.toFixed(
      1
    )}%\n`;
    csv += `Events This Month,${analytics.eventsThisMonth}\n`;
    csv += `Attendees This Month,${analytics.attendeesThisMonth}\n`;
    csv += `Check-ins Today,${analytics.checkInsToday}\n\n`;

    // Events data
    csv += "EVENTS\n";
    csv +=
      "Event Name,Status,Start Date,Location,Attendees,Check-ins,Check-in Rate\n";
    events.forEach((event) => {
      const eventAttendees = attendees.filter((a) => a.eventId === event.id);
      const checkedIn = eventAttendees.filter((a) => a.checkedIn).length;
      const rate =
        eventAttendees.length > 0
          ? (checkedIn / eventAttendees.length) * 100
          : 0;

      const eventName = event.event_stage?.event?.name_vi || event.short_desc_vi || 'Unknown Event';
      const eventStatus = event.status || 1;
      const startDate = new Date(event.show_date * 1000).toLocaleDateString();
      const location = event.event_stage?.venue_vi || 'Unknown Location';

      csv += `"${eventName}",${eventStatus},${startDate},"${location}",${
        eventAttendees.length
      },${checkedIn},${rate.toFixed(1)}%\n`;
    });

    csv += "\nATTENDEES\n";
    csv += "Name,Email,Event,Registered Date,Checked In,Check-in Date\n";
    attendees.forEach((attendee) => {
      const event = events.find((e) => e.id === attendee.eventId);
      const eventName = event?.event_stage?.event?.name_vi || event?.short_desc_vi || 'Unknown';
      csv += `"${attendee.name}","${attendee.email}","${eventName}",${attendee.registeredAt.toLocaleDateString()},${
        attendee.checkedIn ? "Yes" : "No"
      },${
        attendee.checkedInAt ? attendee.checkedInAt.toLocaleDateString() : "N/A"
      }\n`;
    });

    return csv;
  }

  /**
   * Export data to JSON format
   */
  static exportToJSON(events: EventListItem[], attendees: Attendee[]): ExportData {
    const analytics = this.generateAnalytics(events, attendees);

    return {
      events,
      attendees,
      summary: {
        totalEvents: analytics.totalEvents,
        totalAttendees: analytics.totalAttendees,
        totalCheckedIn: analytics.totalCheckedIn,
        checkInRate: analytics.overallCheckInRate,
        generatedAt: new Date().toISOString(),
      },
    };
  }

  /**
   * Download file with given content
   */
  static downloadFile(
    content: string,
    filename: string,
    mimeType: string = "text/plain"
  ): void {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Generate and download CSV report
   */
  static downloadCSVReport(events: EventListItem[], attendees: Attendee[]): void {
    const csv = this.exportToCSV(events, attendees);
    const filename = `event-analytics-${format(new Date(), "yyyy-MM-dd")}.csv`;
    this.downloadFile(csv, filename, "text/csv");
  }

  /**
   * Generate and download JSON report
   */
  static downloadJSONReport(events: EventListItem[], attendees: Attendee[]): void {
    const data = this.exportToJSON(events, attendees);
    const json = JSON.stringify(data, null, 2);
    const filename = `event-data-${format(new Date(), "yyyy-MM-dd")}.json`;
    this.downloadFile(json, filename, "application/json");
  }
}
