import React from 'react';

const RecentEventsListSkeleton: React.FC = () => {
  return (
    <div className="bg-white shadow rounded-lg animate-pulse transition-opacity duration-300 ease-in-out">
      <div className="px-3 py-4 sm:px-4 sm:py-5 lg:p-6">
        {/* Header skeleton */}
        <div className="h-5 sm:h-6 bg-gray-200 rounded w-32 sm:w-40 mb-4 sm:mb-6" />
        
        <div className="mt-4 sm:mt-6 flow-root">
          <ul className="-my-3 sm:-my-5 divide-y divide-gray-200">
            {[...Array(3)].map((_, index) => (
              <li key={index} className="py-3 sm:py-4">
                <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                  <div className="flex-1 min-w-0">
                    {/* Event name skeleton */}
                    <div className="h-4 bg-gray-200 rounded w-3/4 sm:w-48 mb-2" />
                    {/* Event details skeleton */}
                    <div className="h-3 bg-gray-200 rounded w-1/2 sm:w-32" />
                  </div>
                  <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2">
                    {/* Status badge skeleton */}
                    <div className="h-5 bg-gray-200 rounded-full w-16 self-start sm:self-auto" />
                    {/* Check-in info skeleton */}
                    <div className="h-3 bg-gray-200 rounded w-20" />
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default RecentEventsListSkeleton;