import type { DashboardError } from "../types";
import { DashboardErrorType } from "../types";

export const parseError = (error: unknown): DashboardError => {
  // Handle network errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return {
      type: DashboardErrorType.NETWORK_ERROR,
      message: 'Network connection failed',
      retryable: true
    };
  }

  // Handle timeout errors
  if (error instanceof Error && error.name === 'AbortError') {
    return {
      type: DashboardErrorType.TIMEOUT_ERROR,
      message: 'Request timed out',
      retryable: true
    };
  }

  // Handle API errors with status codes
  if (error && typeof error === 'object' && 'status' in error) {
    const statusCode = (error as any).status;
    let message = 'Server error occurred';
    let retryable = true;

    switch (statusCode) {
      case 400:
        message = 'Bad request - invalid data sent';
        retryable = false;
        break;
      case 401:
        message = 'Authentication required';
        retryable = false;
        break;
      case 403:
        message = 'Access denied';
        retryable = false;
        break;
      case 404:
        message = 'Resource not found';
        retryable = false;
        break;
      case 429:
        message = 'Too many requests - please wait';
        retryable = true;
        break;
      case 500:
        message = 'Internal server error';
        retryable = true;
        break;
      case 502:
        message = 'Bad gateway';
        retryable = true;
        break;
      case 503:
        message = 'Service unavailable';
        retryable = true;
        break;
      case 504:
        message = 'Gateway timeout';
        retryable = true;
        break;
      default:
        message = `Server error (${statusCode})`;
        retryable = statusCode >= 500;
    }

    return {
      type: DashboardErrorType.API_ERROR,
      message,
      retryable,
      statusCode
    };
  }

  // Handle generic Error objects
  if (error instanceof Error) {
    return {
      type: DashboardErrorType.UNKNOWN_ERROR,
      message: error.message || 'An unexpected error occurred',
      retryable: true
    };
  }

  // Handle string errors
  if (typeof error === 'string') {
    return {
      type: DashboardErrorType.UNKNOWN_ERROR,
      message: error,
      retryable: true
    };
  }

  // Fallback for unknown error types
  return {
    type: DashboardErrorType.UNKNOWN_ERROR,
    message: 'An unexpected error occurred',
    retryable: true
  };
};

export const isRetryableError = (error: DashboardError): boolean => {
  return error.retryable;
};

export const getRetryDelay = (attemptNumber: number): number => {
  // Exponential backoff: 1s, 2s, 4s, 8s, max 30s
  return Math.min(1000 * Math.pow(2, attemptNumber - 1), 30000);
};