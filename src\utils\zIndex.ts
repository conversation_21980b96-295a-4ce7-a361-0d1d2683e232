/**
 * Z-index configuration for consistent layering
 * Higher numbers appear on top
 */
export const Z_INDEX = {
  // Base modal layer
  MODAL_BASE: 1000,
  
  // Specific modal types (in order of priority)
  EVENT_MODAL: 1010,           // Event modal (deprecated)
  EVENT_DETAILS_MODAL: 1020,   // Event details modal
  EVENT_DASHBOARD_MODAL: 1030, // Event dashboard modal
  EVENT_SCANNER_MODAL: 1040,   // Scanner modal
  DOORLIST_MODAL: 1050,        // Doorlist modal
  
  // Nested modals (should appear above their parent)
  QR_CODE_MODAL: 1100,         // QR code modal (can be nested)
  
  // Utility modals (highest priority)
  CONFIRMATION_MODAL: 1200,    // Confirmation dialogs
  ERROR_MODAL: 1300,           // Error dialogs
  
  // Toast notifications (should be above everything)
  TOAST: 2000,
} as const;

/**
 * Get the appropriate z-index for a modal type
 */
export const getModalZIndex = (modalType: keyof typeof Z_INDEX): number => {
  return Z_INDEX[modalType];
};

/**
 * Check if a modal should be above another modal
 */
export const isModalAbove = (modalA: keyof typeof Z_INDEX, modalB: keyof typeof Z_INDEX): boolean => {
  return Z_INDEX[modalA] > Z_INDEX[modalB];
};
