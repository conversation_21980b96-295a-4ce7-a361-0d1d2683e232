import { create } from "zustand";
import { persist } from "zustand/middleware";
import { AuthService } from "../services/authService";
import type { AuthUser, LoginCredentials } from "../types";

interface AuthState {
  user: AuthUser | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  
  // Legacy method for demo compatibility
  loginDemo: (user: AuthUser) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (credentials: LoginCredentials) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await AuthService.login(credentials);
          
          // Convert LoginResponse to AuthUser format
          const user: AuthUser = {
            id: response.id,
            phone: response.phone,
            phone_prefix: response.phone_prefix,
            email: response.email,
            status: response.status,
            name: response.name,
            lastname: response.lastname,
            role: response.role,
            token: response.token,
          };
          
          set({
            user,
            token: response.token,
            refreshToken: null,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : "Login failed";
          set({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },

      refreshAuth: async () => {
        const { refreshToken } = get();
        
        if (!refreshToken) {
          throw new Error("No refresh token available");
        }

        try {
          const response = await AuthService.refreshToken();
          
          set({
            token: response.token,
            refreshToken: response.refreshToken,
            error: null,
          });
        } catch (error) {
          // If refresh fails, logout user
          set({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            error: "Session expired",
          });
          throw error;
        }
      },

      clearError: () => set({ error: null }),
      
      setLoading: (loading: boolean) => set({ isLoading: loading }),

      // Legacy method for demo compatibility
      loginDemo: (user: AuthUser) =>
        set({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        }),
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
