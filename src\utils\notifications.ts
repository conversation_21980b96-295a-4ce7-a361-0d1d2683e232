// Simple notification system - you can replace this with your preferred toast library
export interface NotificationOptions {
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  persistent?: boolean;
}

class NotificationManager {
  private notifications: Map<string, NotificationOptions> = new Map();
  private listeners: Set<(notifications: NotificationOptions[]) => void> = new Set();

  show(options: NotificationOptions): string {
    const id = Math.random().toString(36).substr(2, 9);
    this.notifications.set(id, options);
    this.notifyListeners();

    // Auto-remove after duration (default 5 seconds)
    if (!options.persistent) {
      setTimeout(() => {
        this.remove(id);
      }, options.duration || 5000);
    }

    return id;
  }

  remove(id: string): void {
    this.notifications.delete(id);
    this.notifyListeners();
  }

  clear(): void {
    this.notifications.clear();
    this.notifyListeners();
  }

  subscribe(listener: (notifications: NotificationOptions[]) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    const notifications = Array.from(this.notifications.values());
    this.listeners.forEach(listener => listener(notifications));
  }
}

export const notificationManager = new NotificationManager();

// Convenience methods
export const showSuccess = (message: string, title?: string) => {
  return notificationManager.show({ type: 'success', message, title });
};

export const showError = (message: string, title?: string) => {
  return notificationManager.show({ type: 'error', message, title });
};

export const showWarning = (message: string, title?: string) => {
  return notificationManager.show({ type: 'warning', message, title });
};

export const showInfo = (message: string, title?: string) => {
  return notificationManager.show({ type: 'info', message, title });
};

// Rate limiting modal handler
export const showTooManyRequestsModal = (retryAfterSeconds: number): void => {
  showWarning(
    `Too many requests. Please wait ${retryAfterSeconds} seconds before trying again.`,
    'Rate Limited'
  );
};