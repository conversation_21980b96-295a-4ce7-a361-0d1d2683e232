import { ApiService } from "./apiService";
import { API_ENDPOINTS } from "../config/api";
import type { AuthUser, LoginCredentials, RegisterData, ApiResponse } from "../types";

export interface LoginResponse {
  id: string;
  phone: string;
  phone_prefix: string;
  email: string;
  status: number;
  name: string;
  lastname: string;
  role: "organizer" | "admin";
  token: string;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
}

export class AuthService {
  /**
   * Login user
   */
  static async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const response = await ApiService.post<ApiResponse<LoginResponse>>(
        API_ENDPOINTS.AUTH.LOGIN,
        credentials,
        { noRetry: true } // Don't retry auth requests
      );
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Login failed");
    } catch (error) {
      console.error("Login error:", error);
      throw error;
    }
  }

  /**
   * Register new user
   */
  static async register(userData: RegisterData): Promise<LoginResponse> {
    try {
      const response = await ApiService.post<ApiResponse<LoginResponse>>(
        API_ENDPOINTS.AUTH.REGISTER,
        userData,
        { noRetry: true }
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Registration failed");
    } catch (error) {
      console.error("Registration error:", error);
      throw error;
    }
  }

  /**
   * Refresh authentication token
   */
  static async refreshToken(): Promise<RefreshTokenResponse> {
    try {
      const response = await ApiService.post<ApiResponse<RefreshTokenResponse>>(
        API_ENDPOINTS.AUTH.REFRESH,
        {},
        { noRetry: true }
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Token refresh failed");
    } catch (error) {
      console.error("Token refresh error:", error);
      throw error;
    }
  }

  /**
   * Get current user profile
   */
  static async getProfile(): Promise<AuthUser> {
    try {
      const response = await ApiService.get<ApiResponse<AuthUser>>(API_ENDPOINTS.AUTH.PROFILE);
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to get profile");
    } catch (error) {
      console.error("Get profile error:", error);
      throw error;
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(userData: Partial<AuthUser>): Promise<AuthUser> {
    try {
      const response = await ApiService.put<ApiResponse<AuthUser>>(
        API_ENDPOINTS.AUTH.PROFILE,
        userData
      );
      
      if (response.status === 200 && response.data) {
        return response.data;
      }
      
      throw new Error(response.message || "Failed to update profile");
    } catch (error) {
      console.error("Update profile error:", error);
      throw error;
    }
  }

  /**
   * Change password
   */
  static async changePassword(data: {
    currentPassword: string;
    newPassword: string;
  }): Promise<void> {
    try {
      const response = await ApiService.post<ApiResponse<void>>(
        API_ENDPOINTS.AUTH.CHANGE_PASSWORD,
        data,
        { noRetry: true }
      );
      
      if (response.status !== 200) {
        throw new Error(response.message || "Failed to change password");
      }
    } catch (error) {
      console.error("Change password error:", error);
      throw error;
    }
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(email: string): Promise<void> {
    try {
      const response = await ApiService.post<ApiResponse<void>>(
        API_ENDPOINTS.AUTH.FORGOT_PASSWORD,
        { email },
        { noRetry: true }
      );
      
      if (response.status !== 200) {
        throw new Error(response.message || "Failed to request password reset");
      }
    } catch (error) {
      console.error("Password reset request error:", error);
      throw error;
    }
  }

  /**
   * Reset password with token
   */
  static async resetPassword(data: {
    token: string;
    newPassword: string;
  }): Promise<void> {
    try {
      const response = await ApiService.post<ApiResponse<void>>(
        API_ENDPOINTS.AUTH.RESET_PASSWORD,
        data,
        { noRetry: true }
      );
      
      if (response.status !== 200) {
        throw new Error(response.message || "Failed to reset password");
      }
    } catch (error) {
      console.error("Password reset error:", error);
      throw error;
    }
  }
}