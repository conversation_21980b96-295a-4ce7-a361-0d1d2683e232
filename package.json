{"name": "vtix-scanner", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@auth/core": "^0.34.2", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.81.2", "axios": "^1.10.0", "date-fns": "^4.1.0", "lucide-react": "^0.523.0", "next-auth": "^4.24.11", "qr-scanner": "^1.4.2", "qrcode": "^1.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-router-dom": "^7.6.2", "recharts": "^3.0.2", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^24.0.4", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}