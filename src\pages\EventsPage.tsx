import { useState, useEffect } from "react";
import {
  Calendar,
  MapPin,
  Users,
  Eye,
  MoreVertical,
  QrCode,
  Bar<PERSON>hart3,
  User<PERSON><PERSON><PERSON>,
} from "lucide-react";

import EventDetailsModal from "../components/EventDetailsModal";
import EventDashboardModal from "../components/EventDashboardModal";
import EventScannerModal from "../components/EventScannerModal";
import DoorlistModal from "../components/DoorlistModal";
import { useEventStore } from "../store/eventStore";
import { EventDateStatus, type Event, type EventListItem } from "../types";
import {
  transformEventListItem,
  isEventOngoing,
  isEventUpcoming,
  isEventCompleted,
} from "../utils/eventTransform";

type EventTab = "ongoing" | "upcoming" | "completed";

const EventsPage = () => {
  const { events, isLoading, setSelectedEvent, getEvents } = useEventStore();

  const [activeTab, setActiveTab] = useState<EventTab>("ongoing");

  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isDashboardModalOpen, setIsDashboardModalOpen] = useState(false);
  const [isScannerModalOpen, setIsScannerModalOpen] = useState(false);
  const [isDoorlistModalOpen, setIsDoorlistModalOpen] = useState(false);
  const [selectedEvent, setSelectedEventLocal] = useState<Event | null>(null);
  const [selectedEventDateId, setSelectedEventDateId] = useState<string | null>(
    null
  );
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);

  useEffect(() => {
    (async () => {
      await getEvents();
    })();
  }, [events.length, getEvents]);

  // Filter events by tab using eventDateStatus
  const filteredEvents = events.filter((event) => {
    // Transform to get the base event for date checking
    // const baseEvent = event.event_stage?.event;
    if (!event) return false;
    switch (activeTab) {
      case "ongoing":
        return isEventOngoing(event);
      case "upcoming":
        return isEventUpcoming(event);
      case "completed":
        return isEventCompleted(event);
      default:
        return true;
    }
  });

  const handleViewEvent = (eventListItem: EventListItem) => {
    const event = transformEventListItem(eventListItem);
    setSelectedEventLocal(event);
    setSelectedEvent(event);
    setIsDetailsModalOpen(true);
    setDropdownOpen(null);
  };

  const handleDashboardView = (eventListItem: EventListItem) => {
    const event = transformEventListItem(eventListItem);
    setSelectedEventLocal(event);
    setSelectedEvent(event);
    setSelectedEventDateId(eventListItem.id); // Store the event_date_id
    setIsDashboardModalOpen(true);
    setDropdownOpen(null);
  };

  const handleScannerView = (eventListItem: EventListItem) => {
    const event = transformEventListItem(eventListItem);
    setSelectedEventLocal(event);
    setSelectedEvent(event);
    setSelectedEventDateId(eventListItem.id); // Store the event_date_id
    setIsScannerModalOpen(true);
    setDropdownOpen(null);
  };

  const handleDoorlistView = (eventListItem: EventListItem) => {
    const event = transformEventListItem(eventListItem);
    setSelectedEventLocal(event);
    setSelectedEvent(event);
    setSelectedEventDateId(eventListItem.id); // Store the event_date_id
    setIsDoorlistModalOpen(true);
    setDropdownOpen(null);
  };

  const getStatusColor = (status: number) => {
    switch (status) {
      case 1:
        return "bg-green-100 text-green-800";
      case 2:
        return "bg-gray-100 text-gray-800";
      case 3:
        return "bg-blue-100 text-blue-800";
      case 0:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusText = (status: number) => {
    switch (status) {
      case 1:
        return "Active";
      case 2:
        return "Draft";
      case 3:
        return "Completed";
      case 0:
        return "Cancelled";
      default:
        return "Unknown";
    }
  };

  const formatDate = (timestamp: number) => {
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(timestamp * 1000));
  };

  const tabs = [
    {
      id: "ongoing" as EventTab,
      name: "Đang diễn ra",
      count: events.filter((e) => e.status === EventDateStatus.ONGOING).length,
    },
    {
      id: "upcoming" as EventTab,
      name: "Sắp diễn ra",
      count: events.filter((e) => e.status === EventDateStatus.UPCOMING).length,
    },
    {
      id: "completed" as EventTab,
      name: "Đã diễn ra",
      count: events.filter(
        (e) => e.status === EventDateStatus.COMPLETED || e.status === 3
      ).length,
    },
  ];

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
        <div className="text-center sm:text-left">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">
            Events
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your events and track attendance.
          </p>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white shadow rounded-lg">
        <div className="border-b border-gray-200">
          <nav
            className="-mb-px flex space-x-4 sm:space-x-8 px-3 sm:px-6 overflow-x-auto"
            aria-label="Tabs"
          >
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`whitespace-nowrap py-3 sm:py-4 px-1 border-b-2 font-medium text-xs sm:text-sm flex-shrink-0 ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <span className="hidden sm:inline">{tab.name}</span>
                <span className="sm:hidden">{tab.name.split(" ")[0]}</span>
                <span
                  className={`ml-1 sm:ml-2 py-0.5 px-1.5 sm:px-2.5 rounded-full text-xs ${
                    activeTab === tab.id
                      ? "bg-blue-100 text-blue-600"
                      : "bg-gray-100 text-gray-900"
                  }`}
                >
                  {tab.count}
                </span>
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-3 sm:p-6">
          {isLoading ? (
            <div className="text-center py-8 sm:py-12">
              <div className="inline-block animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600"></div>
              <p className="mt-2 text-sm text-gray-500">Loading events...</p>
            </div>
          ) : filteredEvents.length === 0 ? (
            <div className="text-center py-8 sm:py-12">
              <Calendar className="h-12 w-12 sm:h-16 sm:w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">
                No events in this category
              </h3>
              <p className="text-sm sm:text-base text-gray-500 mb-4">
                {activeTab === "ongoing" && "No events are currently running."}
                {activeTab === "upcoming" && "No upcoming events scheduled."}
                {activeTab === "completed" && "No completed events found."}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-3">
              {filteredEvents.map((event) => {
                return (
                  <div
                    key={event.id}
                    className="bg-white shadow rounded-lg border border-gray-200 hover:shadow-lg transition-shadow card-with-dropdown"
                  >
                    {/* Event Poster */}
                    {event.event_stage.event.poster_vi && (
                      <div className="aspect-w-16 aspect-h-9 rounded-t-lg overflow-hidden">
                        <img
                          src={`/event/${event.event_stage.event.poster_vi}`}
                          alt={event.event_stage.event.name_vi}
                          className="w-full h-48 object-cover"
                          onError={(e) => {
                            (e.target as HTMLImageElement).style.display =
                              "none";
                          }}
                        />
                      </div>
                    )}
                    <div className="p-4 sm:p-6">
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-2 sm:space-y-0">
                        <div className="flex-1 min-w-0">
                          <h3 className="text-base sm:text-lg font-medium text-gray-900 truncate">
                            {event.event_stage.event.name_vi}
                          </h3>
                          <p className="mt-1 text-sm text-gray-500 line-clamp-2">
                            {event.event_stage.event.meta_description_vi}
                          </p>
                        </div>
                        <span
                          className={`inline-flex items-center px-2 sm:px-2.5 py-0.5 rounded-full text-xs font-medium self-start sm:self-auto ${getStatusColor(
                            event.status
                          )}`}
                        >
                          {getStatusText(event.status)}
                        </span>
                      </div>

                      <div className="mt-3 sm:mt-4 space-y-1 sm:space-y-2">
                        <div className="flex items-center text-xs sm:text-sm text-gray-500">
                          <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                          <span className="truncate">
                            {formatDate(event.show_from)} -{" "}
                            {formatDate(event.show_to)}
                          </span>
                        </div>
                        <div className="flex items-center text-xs sm:text-sm text-gray-500">
                          <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                          <span className="truncate">
                            {event.event_stage.venue_vi}
                          </span>
                        </div>
                        <div className="flex items-center text-xs sm:text-sm text-gray-500">
                          <Users className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                          <span>
                            {event.total_ticket} tickets sold
                            {!event.is_unlimited &&
                              event.max_order &&
                              ` / ${event.max_order} max per order`}
                          </span>
                        </div>
                        {event.is_free_ticket && (
                          <div className="flex items-center text-xs sm:text-sm text-green-600">
                            <span className="font-medium">Free Event</span>
                          </div>
                        )}
                      </div>

                      <div className="mt-3 sm:mt-4 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                        <div className="text-xs sm:text-sm text-gray-500">
                          {event.total_checked_in}/{event.total_ticket} checked
                          in
                        </div>
                        <div className="text-xs sm:text-sm text-gray-500">
                          {event.service_fee &&
                            `Service fee: ${event.service_fee}`}
                          {event.event_stage.event.code && (
                            <span className="ml-2 px-2 py-1 bg-gray-100 rounded text-xs">
                              {event.event_stage.event.code}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="mt-4 sm:mt-6 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                        <button
                          onClick={() => handleDashboardView(event)}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                          title="View Dashboard"
                        >
                          <BarChart3 className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                          <span className="hidden sm:inline">Dashboard</span>
                          <span className="sm:hidden">Stats</span>
                        </button>
                        <button
                          onClick={() => handleScannerView(event)}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-xs sm:text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
                          title="Open Scanner"
                        >
                          <QrCode className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                          Scanner
                        </button>
                        <button
                          onClick={() => handleDoorlistView(event)}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-xs sm:text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                          title="View Doorlist"
                        >
                          <UserCheck className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                          <span className="hidden sm:inline">Doorlist</span>
                          <span className="sm:hidden">List</span>
                        </button>
                      </div>

                      {/* More Actions Dropdown */}
                      {/* <div className="mt-2 sm:mt-3 dropdown-container">
                        <button
                          onClick={() =>
                            setDropdownOpen(
                              dropdownOpen === event.id ? null : event.id
                            )
                          }
                          className="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <MoreVertical className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                          More Actions
                        </button>

                        {dropdownOpen === event.id && (
                          <div className="dropdown-menu right-0 mt-2 w-full">
                            <div className="py-1">
                              <button
                                onClick={() => handleViewEvent(event)}
                                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </button>
                            </div>
                          </div>
                        )}
                      </div> */}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {/* Modals */}

      <EventDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedEventLocal(null);
          setSelectedEvent(null);
        }}
        event={selectedEvent}
      />

      <EventDashboardModal
        isOpen={isDashboardModalOpen}
        onClose={() => {
          setIsDashboardModalOpen(false);
          setSelectedEventLocal(null);
          setSelectedEvent(null);
          setSelectedEventDateId(null);
        }}
        event={selectedEvent}
        eventDateId={selectedEventDateId}
      />

      <EventScannerModal
        isOpen={isScannerModalOpen}
        onClose={() => {
          setIsScannerModalOpen(false);
          setSelectedEventLocal(null);
          setSelectedEvent(null);
          setSelectedEventDateId(null);
        }}
        event={selectedEvent}
        eventDateId={selectedEventDateId}
      />

      <DoorlistModal
        isOpen={isDoorlistModalOpen}
        onClose={() => {
          setIsDoorlistModalOpen(false);
          setSelectedEventLocal(null);
          setSelectedEvent(null);
          setSelectedEventDateId(null);
        }}
        event={selectedEvent}
        eventDateId={selectedEventDateId}
      />
    </div>
  );
};

export default EventsPage;
