@import "tailwindcss";

/* Custom utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
}

/* Mobile-first responsive typography */
.text-responsive-xs {
  font-size: 0.75rem; /* 12px */
  line-height: 1rem; /* 16px */
}

@media (min-width: 640px) {
  .text-responsive-xs {
    font-size: 0.875rem; /* 14px */
    line-height: 1.25rem; /* 20px */
  }
}

.text-responsive-sm {
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
}

@media (min-width: 640px) {
  .text-responsive-sm {
    font-size: 1rem; /* 16px */
    line-height: 1.5rem; /* 24px */
  }
}

.text-responsive-base {
  font-size: 1rem; /* 16px */
  line-height: 1.5rem; /* 24px */
}

@media (min-width: 640px) {
  .text-responsive-base {
    font-size: 1.125rem; /* 18px */
    line-height: 1.75rem; /* 28px */
  }
}

/* Mobile-first spacing utilities */
.space-responsive-x > :not([hidden]) ~ :not([hidden]) {
  margin-left: 0.5rem; /* 8px */
}

@media (min-width: 640px) {
  .space-responsive-x > :not([hidden]) ~ :not([hidden]) {
    margin-left: 1rem; /* 16px */
  }
}

.space-responsive-y > :not([hidden]) ~ :not([hidden]) {
  margin-top: 0.75rem; /* 12px */
}

@media (min-width: 640px) {
  .space-responsive-y > :not([hidden]) ~ :not([hidden]) {
    margin-top: 1.5rem; /* 24px */
  }
}

/* Mobile-optimized padding */
.p-responsive {
  padding: 0.75rem; /* 12px */
}

@media (min-width: 640px) {
  .p-responsive {
    padding: 1rem; /* 16px */
  }
}

@media (min-width: 1024px) {
  .p-responsive {
    padding: 1.5rem; /* 24px */
  }
}

/* Mobile-optimized margins */
.m-responsive {
  margin: 0.5rem; /* 8px */
}

@media (min-width: 640px) {
  .m-responsive {
    margin: 1rem; /* 16px */
  }
}

/* Responsive grid gaps */
.gap-responsive {
  gap: 0.75rem; /* 12px */
}

@media (min-width: 640px) {
  .gap-responsive {
    gap: 1rem; /* 16px */
  }
}

@media (min-width: 1024px) {
  .gap-responsive {
    gap: 1.5rem; /* 24px */
  }
}

/* Mobile-first button sizing */
.btn-responsive {
  padding: 0.75rem 1rem; /* 12px 16px */
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
}

@media (min-width: 640px) {
  .btn-responsive {
    padding: 0.5rem 1rem; /* 8px 16px */
    font-size: 0.875rem; /* 14px */
    line-height: 1.25rem; /* 20px */
  }
}

/* Responsive container max-widths */
.container-responsive {
  width: 100%;
  padding-left: 1rem; /* 16px */
  padding-right: 1rem; /* 16px */
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: 640px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1.5rem; /* 24px */
    padding-right: 1.5rem; /* 24px */
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
    padding-left: 2rem; /* 32px */
    padding-right: 2rem; /* 32px */
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

/* Mobile-first card styling */
.card-responsive {
  background-color: white;
  border-radius: 0.5rem; /* 8px */
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1rem; /* 16px */
}

@media (min-width: 640px) {
  .card-responsive {
    border-radius: 0.75rem; /* 12px */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    padding: 1.5rem; /* 24px */
  }
}

/* Responsive icon sizing */
.icon-responsive {
  width: 1rem; /* 16px */
  height: 1rem; /* 16px */
}

@media (min-width: 640px) {
  .icon-responsive {
    width: 1.25rem; /* 20px */
    height: 1.25rem; /* 20px */
  }
}

.icon-responsive-lg {
  width: 1.25rem; /* 20px */
  height: 1.25rem; /* 20px */
}

@media (min-width: 640px) {
  .icon-responsive-lg {
    width: 1.5rem; /* 24px */
    height: 1.5rem; /* 24px */
  }
}

/* Dropdown positioning fix */
.dropdown-container {
  position: relative;
  z-index: 1;
}

.dropdown-menu {
  position: absolute;
  z-index: 50;
  min-width: 12rem; /* 192px */
  background: white;
  border-radius: 0.375rem; /* 6px */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Ensure dropdowns appear above other content */
.card-with-dropdown {
  position: relative;
  z-index: 1;
}

.card-with-dropdown:hover,
.card-with-dropdown:focus-within {
  z-index: 10;
}

/* Mobile-first modal improvements */
@media (max-width: 639px) {
  /* Ensure modals take full height on mobile */
  .modal-mobile {
    height: 100vh;
    border-radius: 0;
    border-top-left-radius: 1rem;
    border-top-right-radius: 1rem;
  }

  /* Prevent zoom on input focus on iOS */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px;
  }

  /* Improve touch targets */
  button,
  .btn,
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Smooth modal animations */
.modal-enter {
  opacity: 0;
  transform: translateY(100%);
}

.modal-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms ease-out, transform 300ms ease-out;
}

.modal-exit {
  opacity: 1;
  transform: translateY(0);
}

.modal-exit-active {
  opacity: 0;
  transform: translateY(100%);
  transition: opacity 300ms ease-in, transform 300ms ease-in;
}

@media (min-width: 640px) {
  .modal-enter {
    transform: scale(0.95);
  }

  .modal-enter-active {
    transform: scale(1);
  }

  .modal-exit {
    transform: scale(1);
  }

  .modal-exit-active {
    transform: scale(0.95);
  }
}
