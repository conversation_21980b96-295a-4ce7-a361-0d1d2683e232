import React from 'react';

interface SkeletonWrapperProps {
  isLoading: boolean;
  skeleton: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

const SkeletonWrapper: React.FC<SkeletonWrapperProps> = ({
  isLoading,
  skeleton,
  children,
  className = '',
}) => {
  return (
    <div className={`relative ${className}`}>
      {isLoading ? (
        <div className="animate-pulse transition-opacity duration-300 ease-in-out">
          {skeleton}
        </div>
      ) : (
        <div className="transition-opacity duration-300 ease-in-out opacity-100">
          {children}
        </div>
      )}
    </div>
  );
};

export default SkeletonWrapper;