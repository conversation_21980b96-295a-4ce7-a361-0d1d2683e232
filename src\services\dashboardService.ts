import { ApiService } from "./apiService";
import { API_ENDPOINTS } from "../config/api";
import type { ApiResponse } from "../types";

// Dashboard API Response Interfaces
export interface DashboardSeat {
  id: number;
  event_id: string;
  zone_detail_id: number;
  event_stage_id: number;
  event_stage_date_id: string;
  row_code: string;
  total_seat_row: number;
  max_seat_row: number;
  seat_used: number;
  price: number;
  color: string;
  active: boolean;
  seat_position: Record<string, string>;
  seat_position_table_name: Record<string, string>;
  seat_pos_start: number;
  total_seat_today: number;
  deleted: boolean;
  created_at: string;
  updated_at: string;
  order_sort: number;
  totalCheckin: number;
  totalTickets: number;
  totalPrices: number;
}

export interface DashboardZoneDetail {
  id: number;
  event_id: string;
  event_stage_id: number;
  event_date_id: number;
  zone_map_position: string;
  zone_name: string;
  zone_image: string;
  ticket_price_desc_vi: string;
  seat_type: number;
  ticket_price_desc_en: string;
  area_shape: string;
  nft_name: string;
  nft_symbol: string;
  nft_image: string;
  active: boolean;
  is_reversed: number;
  buy_required: number;
  get_free_quantity: number;
  email_poster: string | null;
  deleted: boolean;
  created_at: string;
  updated_at: string;
  color: string;
  benifits_vi: string | null;
  benifits_en: string | null;
  color_text: string;
  zone_title: string | null;
  description_vi: string | null;
  description_en: string | null;
  attributes: Record<string, any>;
  seats: DashboardSeat[];
}

export interface DashboardEventStage {
  id: string;
  event_id: string;
  poster_stage_vi: string;
  poster_stage_en: string;
  stage_type: number;
  venue_vi: string;
  ticket_price_desc_vi: string;
  ticket_price_desc_en: string;
  venue_en: string;
  active: boolean;
  deleted: boolean;
  created_at: string;
  updated_at: string;
  event: {
    id: string;
    name_vi: string;
    venue_vi: string;
    meta_title_vi: string;
    showdate_from: number;
    showdate_to: number;
    public_sale: number;
    public_sale_to: number;
    event_type: number;
  };
}

export interface DashboardEventDate {
  id: number;
  event_stage_id: number;
  event_id: string;
  show_date: number;
  show_from: number;
  show_to: number;
  public_sale: number;
  public_sale_to: number;
  short_desc_vi: string;
  short_desc_en: string;
  ticket_price_desc_vi: string;
  ticket_price_desc_en: string;
  zone_map: string;
  zone_price_image_vi: string;
  zone_price_image_en: string;
  service_fee: string;
  active: boolean;
  is_free_ticket: boolean;
  deleted: boolean;
  created_at: string;
  updated_at: string;
  allow_check_in: number;
  status: number;
  show_stage: number;
  salepoint_code: string | null;
  seat_type: number;
  ticket_type: string;
  email_note_vi: string;
  email_note_en: string;
  attributes: {
    width: string;
    height: string;
  };
  min_order: number;
  max_order: number;
  countdown: number;
  is_unlimited: boolean;
  email_poster_vi: string | null;
  email_poster_en: string | null;
  notify_email: string | null;
  content_vi: string | null;
  content_en: string | null;
  event_stage: DashboardEventStage;
  zone_detail: DashboardZoneDetail[];
  totalTicketIssued: number;
  totalTicketSold: number;
  totalCheckedin: number;
  totalRevenue: number;
}

export interface DashboardData {
  eventDate: DashboardEventDate;
  stats: {
    totalTicketIssued: number;
    totalTicketSold: number;
    totalCheckedin: number;
    totalRevenue: number;
    checkInRate: number;
    salesRate: number;
  };
  zoneStats: Array<{
    zoneName: string;
    totalSeats: number;
    soldSeats: number;
    checkedInSeats: number;
    revenue: number;
    color: string;
  }>;
}

export class DashboardService {
  /**
   * Get dashboard data for a specific event date
   */
  static async getDashboardData(eventDateId: string): Promise<DashboardData> {
    try {
      const response = await ApiService.request<ApiResponse<DashboardEventDate>>(
        API_ENDPOINTS.DASHBOARD.GET(eventDateId),
        "get"
      );

      if (response.status === 200 && response.data) {
        const eventDate = response.data;
        
        // Calculate zone statistics
        const zoneStats = eventDate.zone_detail.map(zone => {
          const totalSeats = zone.seats.reduce((sum, seat) => sum + seat.total_seat_row, 0);
          const soldSeats = zone.seats.reduce((sum, seat) => sum + seat.totalTickets, 0);
          const checkedInSeats = zone.seats.reduce((sum, seat) => sum + seat.totalCheckin, 0);
          const revenue = zone.seats.reduce((sum, seat) => sum + seat.totalPrices, 0);
          
          return {
            zoneName: zone.zone_name,
            totalSeats,
            soldSeats,
            checkedInSeats,
            revenue,
            color: zone.color,
          };
        });

        // Calculate overall statistics
        const stats = {
          totalTicketIssued: eventDate.totalTicketIssued,
          totalTicketSold: eventDate.totalTicketSold,
          totalCheckedin: eventDate.totalCheckedin,
          totalRevenue: eventDate.totalRevenue,
          checkInRate: eventDate.totalTicketSold > 0 
            ? (eventDate.totalCheckedin / eventDate.totalTicketSold) * 100 
            : 0,
          salesRate: eventDate.totalTicketIssued > 0 
            ? (eventDate.totalTicketSold / eventDate.totalTicketIssued) * 100 
            : 0,
        };

        return {
          eventDate,
          stats,
          zoneStats,
        };
      }

      throw new Error(response.message || "Failed to fetch dashboard data");
    } catch (error) {
      console.error("Get dashboard data error:", error);
      throw error;
    }
  }
}
