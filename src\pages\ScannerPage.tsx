import { useState, useEffect } from "react";
import {
  QrC<PERSON>,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  Clock,
  AlertTriangle,
  RefreshCw,
} from "lucide-react";
import QRScanner from "../components/QRScanner";
import type { ScanResult } from "../services/qrScannerService";
import { useEventStore } from "../store/eventStore";
import type { Attendee, Event } from "../types";
import { transformEventListItem } from "../utils/eventTransform";

interface CheckInResult {
  success: boolean;
  attendee?: Attendee;
  event?: Event;
  error?: string;
  timestamp: Date;
}

const ScannerPage = () => {
  const { events, attendees, checkInAttendee, initializeMockData } =
    useEventStore();
  const [isScanning, setIsScanning] = useState(false);
  const [checkInHistory, setCheckInHistory] = useState<CheckInResult[]>([]);
  const [currentResult, setCurrentResult] = useState<CheckInResult | null>(
    null
  );
  const [stats, setStats] = useState({
    totalScans: 0,
    successfulCheckIns: 0,
    failedScans: 0,
  });

  useEffect(() => {
    // Initialize with mock data if no events exist
    if (events.length === 0) {
      initializeMockData();
    }
  }, [events.length, initializeMockData]);

  const handleScan = async (scanResult: ScanResult) => {
    const timestamp = new Date();

    // Update scan statistics
    setStats((prev) => ({
      ...prev,
      totalScans: prev.totalScans + 1,
      failedScans: scanResult.success ? prev.failedScans : prev.failedScans + 1,
    }));

    if (!scanResult.success) {
      const failedResult: CheckInResult = {
        success: false,
        error: scanResult.error || "Invalid QR code",
        timestamp,
      };

      setCurrentResult(failedResult);
      setCheckInHistory((prev) => [failedResult, ...prev.slice(0, 19)]); // Keep last 20
      return;
    }

    try {
      // Find the attendee and event
      const attendee = attendees.find((a) => a.id === scanResult.attendeeId);
      const eventListItem = events.find((e) => e.id === scanResult.eventId);

      if (!attendee || !eventListItem) {
        const notFoundResult: CheckInResult = {
          success: false,
          error: "Attendee or event not found in system",
          timestamp,
        };

        setCurrentResult(notFoundResult);
        setCheckInHistory((prev) => [notFoundResult, ...prev.slice(0, 19)]);
        return;
      }

      const event = transformEventListItem(eventListItem);

      // Check if already checked in
      if (attendee.checkedIn) {
        const alreadyCheckedResult: CheckInResult = {
          success: false,
          attendee,
          event,
          error: `${attendee.name} is already checked in`,
          timestamp,
        };

        setCurrentResult(alreadyCheckedResult);
        setCheckInHistory((prev) => [
          alreadyCheckedResult,
          ...prev.slice(0, 19),
        ]);
        return;
      }

      // Perform check-in
      await checkInAttendee(attendee.id);

      // Success result
      const successResult: CheckInResult = {
        success: true,
        attendee,
        event,
        timestamp,
      };

      setCurrentResult(successResult);
      setCheckInHistory((prev) => [successResult, ...prev.slice(0, 19)]);

      // Update stats
      setStats((prev) => ({
        ...prev,
        successfulCheckIns: prev.successfulCheckIns + 1,
      }));
    } catch (error) {
      const errorResult: CheckInResult = {
        success: false,
        error: "Failed to check in attendee",
        timestamp,
      };

      setCurrentResult(errorResult);
      setCheckInHistory((prev) => [errorResult, ...prev.slice(0, 19)]);
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  const getResultIcon = (result: CheckInResult) => {
    if (result.success) {
      return <CheckCircle className="h-6 w-6 text-green-500" />;
    } else {
      return <XCircle className="h-6 w-6 text-red-500" />;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">QR Code Scanner</h1>
        <p className="mt-1 text-sm text-gray-500">
          Scan QR codes to check in attendees to your events.
        </p>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <QrCode className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Scans
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.totalScans}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-6 w-6 text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Successful Check-ins
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.successfulCheckIns}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircle className="h-6 w-6 text-red-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Failed Scans
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {stats.failedScans}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Scanner */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Camera Scanner
              </h3>
              <button
                onClick={() => setIsScanning(!isScanning)}
                className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white ${
                  isScanning
                    ? "bg-red-600 hover:bg-red-700"
                    : "bg-green-600 hover:bg-green-700"
                } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500`}
              >
                {isScanning ? "Stop Scanner" : "Start Scanner"}
              </button>
            </div>

            {isScanning ? (
              <div className="aspect-square">
                <QRScanner
                  onScan={handleScan}
                  isActive={isScanning}
                  className="w-full h-full"
                />
              </div>
            ) : (
              <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <QrCode className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">
                    Click "Start Scanner" to begin
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Current Result & History */}
        <div className="space-y-6">
          {/* Current Result */}
          {currentResult && (
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Latest Scan Result
                </h3>

                <div
                  className={`p-4 rounded-lg ${
                    currentResult.success
                      ? "bg-green-50 border border-green-200"
                      : "bg-red-50 border border-red-200"
                  }`}
                >
                  <div className="flex items-start">
                    {getResultIcon(currentResult)}
                    <div className="ml-3 flex-1">
                      <h4
                        className={`text-sm font-medium ${
                          currentResult.success
                            ? "text-green-800"
                            : "text-red-800"
                        }`}
                      >
                        {currentResult.success
                          ? "Check-in Successful!"
                          : "Check-in Failed"}
                      </h4>

                      {currentResult.attendee && (
                        <div className="mt-2 space-y-1">
                          <div className="flex items-center text-sm text-gray-600">
                            <User className="h-4 w-4 mr-2" />
                            {currentResult.attendee.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {currentResult.attendee.email}
                          </div>
                        </div>
                      )}

                      {currentResult.event && (
                        <div className="mt-2">
                          <div className="flex items-center text-sm text-gray-600">
                            <Calendar className="h-4 w-4 mr-2" />
                            {currentResult.event.name}
                          </div>
                        </div>
                      )}

                      {currentResult.error && (
                        <div className="mt-2">
                          <div className="flex items-center text-sm text-red-600">
                            <AlertTriangle className="h-4 w-4 mr-2" />
                            {currentResult.error}
                          </div>
                        </div>
                      )}

                      <div className="mt-2 flex items-center text-xs text-gray-500">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatTime(currentResult.timestamp)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Scan History */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Scan History
                </h3>
                <button
                  onClick={() => {
                    setCheckInHistory([]);
                    setStats({
                      totalScans: 0,
                      successfulCheckIns: 0,
                      failedScans: 0,
                    });
                  }}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  <RefreshCw className="h-4 w-4 inline mr-1" />
                  Clear
                </button>
              </div>

              <div className="max-h-96 overflow-y-auto">
                {checkInHistory.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>No scans yet</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {checkInHistory.map((result, index) => (
                      <div
                        key={index}
                        className="flex items-center p-3 border border-gray-200 rounded-lg"
                      >
                        {getResultIcon(result)}
                        <div className="ml-3 flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {result.attendee?.name || "Unknown"}
                            </p>
                            <span className="text-xs text-gray-500">
                              {formatTime(result.timestamp)}
                            </span>
                          </div>
                          {result.error ? (
                            <p className="text-xs text-red-600 truncate">
                              {result.error}
                            </p>
                          ) : (
                            <p className="text-xs text-gray-500 truncate">
                              {result.event?.name}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScannerPage;
