import React from 'react';
import DashboardStatsSkeleton from './DashboardStatsSkeleton';
import RecentEventsListSkeleton from './RecentEventsListSkeleton';
import AnalyticsWidgetSkeleton from './AnalyticsWidgetSkeleton';

const DashboardSkeleton: React.FC = () => {
  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header skeleton */}
      <div className="text-center sm:text-left animate-pulse transition-opacity duration-300 ease-in-out">
        <div className="h-6 sm:h-8 bg-gray-200 rounded w-32 sm:w-40 mb-2" />
        <div className="h-4 bg-gray-200 rounded w-64 sm:w-80" />
      </div>

      {/* Stats Grid Skeleton */}
      <DashboardStatsSkeleton />

      {/* Recent Events Skeleton */}
      <RecentEventsListSkeleton />

      {/* Analytics Section Skeleton */}
      <div className="bg-white shadow rounded-lg animate-pulse transition-opacity duration-300 ease-in-out">
        <div className="px-3 py-4 sm:px-4 sm:py-5 lg:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-2 sm:space-y-0">
            {/* Analytics header skeleton */}
            <div className="flex items-center">
              <div className="h-4 w-4 sm:h-5 sm:w-5 bg-gray-200 rounded mr-2" />
              <div className="h-5 sm:h-6 bg-gray-200 rounded w-32" />
            </div>
            {/* View full analytics link skeleton */}
            <div className="h-4 bg-gray-200 rounded w-36 self-start sm:self-auto" />
          </div>
          <AnalyticsWidgetSkeleton showCharts={false} />
        </div>
      </div>
    </div>
  );
};

export default DashboardSkeleton;